# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'main_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc
class Ui_main_window(object):
    def setupUi(self, main_window):
        if not main_window.objectName():
            main_window.setObjectName(u"main_window")
        main_window.resize(1280, 800)
        sizePolicy = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(main_window.sizePolicy().hasHeightForWidth())
        main_window.setSizePolicy(sizePolicy)
        main_window.setMinimumSize(QSize(800, 600))
        icon = QIcon()
        icon.addFile(u":/other/machradarpro.ico", QSize(), QIcon.Normal, QIcon.Off)
        main_window.setWindowIcon(icon)
        main_window.setStyleSheet(u"QWidget#main_window {\n"
"	background-color: rgb(0, 0, 0);\n"
"}")
        self.verticalLayout = QVBoxLayout(main_window)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.Top_frame = QFrame(main_window)
        self.Top_frame.setObjectName(u"Top_frame")
        self.Top_frame.setMinimumSize(QSize(0, 60))
        self.Top_frame.setMaximumSize(QSize(16777215, 60))
        self.Top_frame.setStyleSheet(u"QFrame#Top_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 rgba(0, 0, 127, 141), stop:1 rgba(0, 0, 0, 72));\n"
"}")
        self.Top_frame.setLineWidth(1)
        self.TOPH_layout = QHBoxLayout(self.Top_frame)
        self.TOPH_layout.setObjectName(u"TOPH_layout")
        self.TOPH_layout.setContentsMargins(10, 20, 10, 10)
        self.TOP1V_layout_ = QVBoxLayout()
        self.TOP1V_layout_.setObjectName(u"TOP1V_layout_")
        self.verticalSpacer_4 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.TOP1V_layout_.addItem(self.verticalSpacer_4)

        self.TOP1H_layout_2 = QHBoxLayout()
        self.TOP1H_layout_2.setObjectName(u"TOP1H_layout_2")
        self.Btn_Setting = QPushButton(self.Top_frame)
        self.Btn_Setting.setObjectName(u"Btn_Setting")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.Btn_Setting.setFont(font)
        self.Btn_Setting.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Setting.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_Setting)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer)

        self.Btn_Filter = QPushButton(self.Top_frame)
        self.Btn_Filter.setObjectName(u"Btn_Filter")
        self.Btn_Filter.setFont(font)
        self.Btn_Filter.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Filter.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_Filter)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_2)

        self.Btn_View = QPushButton(self.Top_frame)
        self.Btn_View.setObjectName(u"Btn_View")
        self.Btn_View.setFont(font)
        self.Btn_View.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_View.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_View)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_3)

        self.Btn_Event = QPushButton(self.Top_frame)
        self.Btn_Event.setObjectName(u"Btn_Event")
        self.Btn_Event.setFont(font)
        self.Btn_Event.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Event.setCheckable(True)
        self.Btn_Event.hide()
        self.TOP1H_layout_2.addWidget(self.Btn_Event)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_4)

        self.Btn_Mode = QPushButton(self.Top_frame)
        self.Btn_Mode.setObjectName(u"Btn_Mode")
        self.Btn_Mode.setFont(font)
        self.Btn_Mode.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Mode.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_Mode)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_5)

        self.Btn_RecordScript = QPushButton(self.Top_frame)
        self.Btn_RecordScript.setObjectName(u"Btn_RecordScript")
        self.Btn_RecordScript.setFont(font)
        self.Btn_RecordScript.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_RecordScript.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_RecordScript)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_6)

        self.Btn_Help = QPushButton(self.Top_frame)
        self.Btn_Help.setObjectName(u"Btn_Help")
        self.Btn_Help.setFont(font)
        self.Btn_Help.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Help.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_Help)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_7)

        self.lable = QLabel(self.Top_frame)
        self.lable.setObjectName(u"lable")
        font1 = QFont()
        font1.setPointSize(12)
        font1.setBold(True)
        font1.setWeight(75)
        self.lable.setFont(font1)
        self.lable.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TOP1H_layout_2.addWidget(self.lable)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP1H_layout_2.addItem(self.horizontalSpacer_8)

        self.Btn_Toolname = QPushButton(self.Top_frame)
        self.Btn_Toolname.setObjectName(u"Btn_Toolname")
        self.Btn_Toolname.setFont(font)
        self.Btn_Toolname.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
        self.Btn_Toolname.setCheckable(True)

        self.TOP1H_layout_2.addWidget(self.Btn_Toolname)


        self.TOP1V_layout_.addLayout(self.TOP1H_layout_2)

        self.verticalSpacer_3 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.TOP1V_layout_.addItem(self.verticalSpacer_3)

        self.TOP1V_layout_.setStretch(0, 1)
        self.TOP1V_layout_.setStretch(1, 5)
        self.TOP1V_layout_.setStretch(2, 2)

        self.TOPH_layout.addLayout(self.TOP1V_layout_)

        self.horizontalSpacer_9 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOPH_layout.addItem(self.horizontalSpacer_9)

        self.TOP2V_layout = QVBoxLayout()
        self.TOP2V_layout.setObjectName(u"TOP2V_layout")
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.TOP2V_layout.addItem(self.verticalSpacer)

        self.TOP2H_layout = QHBoxLayout()
        self.TOP2H_layout.setObjectName(u"TOP2H_layout")
        self.Label_ECO = QLabel(self.Top_frame)
        self.Label_ECO.setObjectName(u"Label_ECO")
        self.Label_ECO.setFont(font)
        self.Label_ECO.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TOP2H_layout.addWidget(self.Label_ECO)

        self.horizontalSpacer_12 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP2H_layout.addItem(self.horizontalSpacer_12)

        self.Btn_CO2 = QPushButton(self.Top_frame)
        self.Btn_CO2.setObjectName(u"Btn_CO2")
        self.Btn_CO2.setFont(font1)
        self.Btn_CO2.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/icon_co2/icon_co2_1.png); }")
        icon1 = QIcon()
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon1.addFile(u":/icon_co2/icon_co2_2.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Disabled, QIcon.On)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Active, QIcon.On)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Selected, QIcon.Off)
        icon1.addFile(u":/icon_co2/icon_co2_0.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn_CO2.setIcon(icon1)
        self.Btn_CO2.setIconSize(QSize(40, 30))
        self.Btn_CO2.setCheckable(True)

        self.TOP2H_layout.addWidget(self.Btn_CO2)

        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP2H_layout.addItem(self.horizontalSpacer_10)

        self.wifi_rssi = QLabel(self.Top_frame)
        self.wifi_rssi.setObjectName(u"wifi_rssi")
        self.wifi_rssi.setFont(font)
        self.wifi_rssi.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TOP2H_layout.addWidget(self.wifi_rssi)

        self.horizontalSpacer_13 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP2H_layout.addItem(self.horizontalSpacer_13)

        self.WIFI = QLabel(self.Top_frame)
        self.WIFI.setObjectName(u"WIFI")
        sizePolicy.setHeightForWidth(self.WIFI.sizePolicy().hasHeightForWidth())
        self.WIFI.setSizePolicy(sizePolicy)
        self.WIFI.setMinimumSize(QSize(41, 31))
        self.WIFI.setMaximumSize(QSize(50, 50))
        self.WIFI.setFont(font1)
        self.WIFI.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_0.png"))
        self.WIFI.setScaledContents(True)

        self.TOP2H_layout.addWidget(self.WIFI)

        self.horizontalSpacer_14 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TOP2H_layout.addItem(self.horizontalSpacer_14)

        self.Battery = QLabel(self.Top_frame)
        self.Battery.setObjectName(u"Battery")
        sizePolicy.setHeightForWidth(self.Battery.sizePolicy().hasHeightForWidth())
        self.Battery.setSizePolicy(sizePolicy)
        self.Battery.setMinimumSize(QSize(51, 31))
        self.Battery.setMaximumSize(QSize(60, 50))
        self.Battery.setFont(font1)
        self.Battery.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Battery.setPixmap(QPixmap(u":/battery/battery_0.png"))
        self.Battery.setScaledContents(True)

        self.TOP2H_layout.addWidget(self.Battery)

        self.TOP2H_layout.setStretch(0, 1)
        self.TOP2H_layout.setStretch(1, 1)
        self.TOP2H_layout.setStretch(2, 1)
        self.TOP2H_layout.setStretch(3, 1)
        self.TOP2H_layout.setStretch(4, 1)
        self.TOP2H_layout.setStretch(5, 1)
        self.TOP2H_layout.setStretch(6, 1)
        self.TOP2H_layout.setStretch(7, 1)
        self.TOP2H_layout.setStretch(8, 3)

        self.TOP2V_layout.addLayout(self.TOP2H_layout)

        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.TOP2V_layout.addItem(self.verticalSpacer_2)

        self.TOP2V_layout.setStretch(0, 1)
        self.TOP2V_layout.setStretch(1, 5)
        self.TOP2V_layout.setStretch(2, 2)

        self.TOPH_layout.addLayout(self.TOP2V_layout)

        self.TOPH_layout.setStretch(0, 10)
        self.TOPH_layout.setStretch(1, 15)
        self.TOPH_layout.setStretch(2, 10)

        self.verticalLayout.addWidget(self.Top_frame)

        self.middle_layout = QHBoxLayout()
        self.middle_layout.setObjectName(u"middle_layout")
        self.Funtion_layout = QVBoxLayout()
        self.Funtion_layout.setObjectName(u"Funtion_layout")
        self.verticalSpacer_9 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.Funtion_layout.addItem(self.verticalSpacer_9)

        self.Btn_Link = QPushButton(main_window)
        self.Btn_Link.setObjectName(u"Btn_Link")
        self.Btn_Link.setFont(font1)
        self.Btn_Link.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }")
        icon2 = QIcon()
        icon2.addFile(u":/btn_link/btn_link_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon2.addFile(u":/btn_link/btn_link_1.png", QSize(), QIcon.Normal, QIcon.On)
        icon2.addFile(u":/btn_link/btn_link_0.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon2.addFile(u":/btn_link/btn_link_0.png", QSize(), QIcon.Disabled, QIcon.On)
        icon2.addFile(u":/btn_link/btn_link_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon2.addFile(u":/btn_link/btn_link_1.png", QSize(), QIcon.Active, QIcon.On)
        icon2.addFile(u":/btn_link/btn_link_0.png", QSize(), QIcon.Selected, QIcon.Off)
        icon2.addFile(u":/btn_link/btn_link_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn_Link.setIcon(icon2)
        self.Btn_Link.setIconSize(QSize(50, 50))
        self.Btn_Link.setCheckable(True)

        self.Funtion_layout.addWidget(self.Btn_Link)

        self.verticalSpacer_8 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.Funtion_layout.addItem(self.verticalSpacer_8)

        self.Btn_Record = QPushButton(main_window)
        self.Btn_Record.setObjectName(u"Btn_Record")
        self.Btn_Record.setFont(font1)
        self.Btn_Record.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }")
        icon3 = QIcon()
        icon3.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon3.addFile(u":/btn_record/btn_record_s1_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon3.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon3.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Disabled, QIcon.On)
        icon3.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon3.addFile(u":/btn_record/btn_record_s1_0.png", QSize(), QIcon.Active, QIcon.On)
        icon3.addFile(u":/btn_record/btn_record_s0_1.png", QSize(), QIcon.Selected, QIcon.Off)
        icon3.addFile(u":/btn_record/btn_record_s1_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn_Record.setIcon(icon3)
        self.Btn_Record.setIconSize(QSize(50, 50))
        self.Btn_Record.setCheckable(True)

        self.Funtion_layout.addWidget(self.Btn_Record)

        self.verticalSpacer_5 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.Funtion_layout.addItem(self.verticalSpacer_5)

        self.scrollArea = QScrollArea(main_window)
        self.scrollArea.setObjectName(u"scrollArea")
        self.scrollArea.setAutoFillBackground(True)
        self.scrollArea.setStyleSheet("QScrollArea {background: black; border: none;}")

        # 設定 QScrollBar 樣式 (分開設定)

        self.scrollArea.verticalScrollBar().setStyleSheet("""

        QScrollBar:vertical {
        background: black;
        width: 2px;
        border: none;
        margin: 0px 0px 0px 0px;
        }

        QScrollBar::handle:vertical {
        background: #7AFEC6;
        min-height: 20px;
        border-radius: 3px;
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        background: none;
        height: 0px;
        }
        QScrollBar::sub-page:vertical, QScrollBar::add-page:vertical {
        background: black;
        }

        """)

        self.scrollArea.horizontalScrollBar().setStyleSheet("""

        QScrollBar:horizontal {
        background: black;
        height: 2px;
        border: none;
        margin: 0px 0px 0px 0px;
        }

        QScrollBar::handle:horizontal {
        background: #7AFEC6;
        min-width: 20px;
        border-radius: 3px;
        }

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        background: none;
        width: 0px;
        }

        QScrollBar::sub-page:horizontal, QScrollBar::add-page:horizontal {
        background: black;
        }

        """)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setAlignment(Qt.AlignRight|Qt.AlignTrailing|Qt.AlignVCenter)
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 85, 404))
        self.scrollAreaWidgetContents.setMinimumSize(QSize(54, 0))
        self.scrollAreaWidgetContents.setLayoutDirection(Qt.LeftToRight)
        self.scrollAreaWidgetContents.setStyleSheet(u"border: none;")
        self.verticalLayout_4 = QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.Btn1_Tare = QPushButton(self.scrollAreaWidgetContents)
        self.Btn1_Tare.setObjectName(u"Btn1_Tare")
        sizePolicy1 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.Btn1_Tare.sizePolicy().hasHeightForWidth())
        self.Btn1_Tare.setSizePolicy(sizePolicy1)
        self.Btn1_Tare.setFont(font1)
        self.Btn1_Tare.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/bnt_vp_tare/btn_vp_tare_1.png); }")
        icon4 = QIcon()
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_2.png", QSize(), QIcon.Normal, QIcon.Off)
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_2.png", QSize(), QIcon.Normal, QIcon.On)
        icon4.addFile(u":/bnt_vp_tare/bnt_vp_cant_tare.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon4.addFile(u":/bnt_vp_tare/bnt_vp_cant_tare.png", QSize(), QIcon.Disabled, QIcon.On)
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_2.png", QSize(), QIcon.Active, QIcon.Off)
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_2.png", QSize(), QIcon.Active, QIcon.On)
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_0.png", QSize(), QIcon.Selected, QIcon.Off)
        icon4.addFile(u":/bnt_vp_tare/btn_vp_tare_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn1_Tare.setIcon(icon4)
        self.Btn1_Tare.setIconSize(QSize(50, 50))
        self.Btn1_Tare.setCheckable(True)
        self.Btn1_Tare.setAutoDefault(True)
        self.Btn1_Tare.setFlat(False)

        self.verticalLayout_4.addWidget(self.Btn1_Tare)

        self.Btn2_BGMode = QPushButton(self.scrollAreaWidgetContents)
        self.Btn2_BGMode.setObjectName(u"Btn2_BGMode")
        sizePolicy1.setHeightForWidth(self.Btn2_BGMode.sizePolicy().hasHeightForWidth())
        self.Btn2_BGMode.setSizePolicy(sizePolicy1)
        self.Btn2_BGMode.setFont(font1)
        self.Btn2_BGMode.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/btn_vp_bgmode/btn_vp_bgmode_1.png); }")
        icon5 = QIcon()
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_2.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_2.png", QSize(), QIcon.Disabled, QIcon.On)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_0.png", QSize(), QIcon.Active, QIcon.On)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_2.png", QSize(), QIcon.Selected, QIcon.Off)
        icon5.addFile(u":/btn_vp_bgmode/btn_vp_bgmode_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn2_BGMode.setIcon(icon5)
        self.Btn2_BGMode.setIconSize(QSize(50, 50))
        self.Btn2_BGMode.setCheckable(True)
        self.Btn2_BGMode.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn2_BGMode)

        self.Btn3_PlotMode = QPushButton(self.scrollAreaWidgetContents)
        self.Btn3_PlotMode.setObjectName(u"Btn3_PlotMode")
        sizePolicy1.setHeightForWidth(self.Btn3_PlotMode.sizePolicy().hasHeightForWidth())
        self.Btn3_PlotMode.setSizePolicy(sizePolicy1)
        self.Btn3_PlotMode.setFont(font1)
        self.Btn3_PlotMode.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/btn_vp_plotmode/btn_vp_plotmode_1.png); }")
        icon6 = QIcon()
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_2.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_2.png", QSize(), QIcon.Disabled, QIcon.On)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_0.png", QSize(), QIcon.Active, QIcon.On)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_0.png", QSize(), QIcon.Selected, QIcon.Off)
        icon6.addFile(u":/btn_vp_plotmode/btn_vp_plotmode_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn3_PlotMode.setIcon(icon6)
        self.Btn3_PlotMode.setIconSize(QSize(50, 50))
        self.Btn3_PlotMode.setCheckable(True)
        self.Btn3_PlotMode.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn3_PlotMode)

        self.Btn8_target = QPushButton(self.scrollAreaWidgetContents)
        self.Btn8_target.setObjectName(u"Btn8_target")
        sizePolicy1.setHeightForWidth(self.Btn8_target.sizePolicy().hasHeightForWidth())
        self.Btn8_target.setSizePolicy(sizePolicy1)
        self.Btn8_target.setFont(font)
        self.Btn8_target.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")
        icon7 = QIcon()
        icon7.addFile(u":/target/target_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon7.addFile(u":/target/target_1.png", QSize(), QIcon.Normal, QIcon.On)
        self.Btn8_target.setIcon(icon7)
        self.Btn8_target.setIconSize(QSize(50, 50))
        self.Btn8_target.setCheckable(True)
        self.Btn8_target.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn8_target)

        self.Btn4_ResetAngle = QPushButton(self.scrollAreaWidgetContents)
        self.Btn4_ResetAngle.setObjectName(u"Btn4_ResetAngle")
        sizePolicy1.setHeightForWidth(self.Btn4_ResetAngle.sizePolicy().hasHeightForWidth())
        self.Btn4_ResetAngle.setSizePolicy(sizePolicy1)
        self.Btn4_ResetAngle.setFont(font1)
        self.Btn4_ResetAngle.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/btn_vp_resetangle/btn_vp_resetangle_1.png); }")
        icon7 = QIcon()
        icon7.addFile(u":/btn_vp_resetangle/btn_vp_resetangle_0.png", QSize(), QIcon.Normal, QIcon.Off)
        self.Btn4_ResetAngle.setIcon(icon7)
        self.Btn4_ResetAngle.setIconSize(QSize(50, 50))
        self.Btn4_ResetAngle.setCheckable(True)
        self.Btn4_ResetAngle.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn4_ResetAngle)

        self.Btn5_cnc_Link = QPushButton(self.scrollAreaWidgetContents)
        self.Btn5_cnc_Link.setObjectName(u"Btn5_cnc_Link")
        sizePolicy1.setHeightForWidth(self.Btn5_cnc_Link.sizePolicy().hasHeightForWidth())
        self.Btn5_cnc_Link.setSizePolicy(sizePolicy1)
        self.Btn5_cnc_Link.setFont(font)
        self.Btn5_cnc_Link.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")
        icon8 = QIcon()
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_0.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_2.png", QSize(), QIcon.Disabled, QIcon.On)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_2.png", QSize(), QIcon.Active, QIcon.Off)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_1.png", QSize(), QIcon.Active, QIcon.On)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_2.png", QSize(), QIcon.Selected, QIcon.Off)
        icon8.addFile(u":/btn_vp_cnc_link/btn_vp_cnc_link_1.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn5_cnc_Link.setIcon(icon8)
        self.Btn5_cnc_Link.setIconSize(QSize(50, 50))
        self.Btn5_cnc_Link.setCheckable(True)
        self.Btn5_cnc_Link.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn5_cnc_Link)

        self.Btn6_angle1 = QPushButton(self.scrollAreaWidgetContents)
        self.Btn6_angle1.setObjectName(u"Btn6_angle1")
        sizePolicy1.setHeightForWidth(self.Btn6_angle1.sizePolicy().hasHeightForWidth())
        self.Btn6_angle1.setSizePolicy(sizePolicy1)
        self.Btn6_angle1.setFont(font1)
        self.Btn6_angle1.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { image: url(:/btn_angle/btn_angle_1_0.png); }")
        icon9 = QIcon()
        icon9.addFile(u":/btn_angle/btn_angle_1_2.png", QSize(), QIcon.Normal, QIcon.Off)
        icon9.addFile(u":/btn_angle/btn_angle_1_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon9.addFile(u":/btn_angle/btn_angle_1_2.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon9.addFile(u":/btn_angle/btn_angle_1_0.png", QSize(), QIcon.Disabled, QIcon.On)
        icon9.addFile(u":/btn_angle/btn_angle_1_1.png", QSize(), QIcon.Active, QIcon.Off)
        icon9.addFile(u":/btn_angle/btn_angle_1_0.png", QSize(), QIcon.Active, QIcon.On)
        icon9.addFile(u":/btn_angle/btn_angle_1_1.png", QSize(), QIcon.Selected, QIcon.Off)
        icon9.addFile(u":/btn_angle/btn_angle_1_0.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn6_angle1.setIcon(icon9)
        self.Btn6_angle1.setIconSize(QSize(50, 50))
        self.Btn6_angle1.setCheckable(True)
        self.Btn6_angle1.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn6_angle1)

        self.Btn7_sleep_mode = QPushButton(self.scrollAreaWidgetContents)
        self.Btn7_sleep_mode.setObjectName(u"Btn7_sleep_mode")
        sizePolicy1.setHeightForWidth(self.Btn7_sleep_mode.sizePolicy().hasHeightForWidth())
        self.Btn7_sleep_mode.setSizePolicy(sizePolicy1)
        self.Btn7_sleep_mode.setFont(font1)
        self.Btn7_sleep_mode.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }")
        icon10 = QIcon()
        icon10.addFile(u":/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png", QSize(), QIcon.Normal, QIcon.Off)
        icon10.addFile(u":/bnt_vp_sleep_mode/btn_vp_sleep_on.png", QSize(), QIcon.Normal, QIcon.On)
        icon10.addFile(u":/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon10.addFile(u":/bnt_vp_sleep_mode/btn_vp_sleep_off.png", QSize(), QIcon.Disabled, QIcon.On)
        icon10.addFile(u":/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png", QSize(), QIcon.Active, QIcon.Off)
        icon10.addFile(u":/bnt_vp_sleep_mode/btn_vp_sleep_on.png", QSize(), QIcon.Active, QIcon.On)
        icon10.addFile(u":/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png", QSize(), QIcon.Selected, QIcon.Off)
        icon10.addFile(u":/bnt_vp_sleep_mode/btn_vp_sleep_on.png", QSize(), QIcon.Selected, QIcon.On)
        self.Btn7_sleep_mode.setIcon(icon10)
        self.Btn7_sleep_mode.setIconSize(QSize(50, 50))
        self.Btn7_sleep_mode.setCheckable(True)
        self.Btn7_sleep_mode.setAutoDefault(True)

        self.verticalLayout_4.addWidget(self.Btn7_sleep_mode)

        self.scrollArea.setWidget(self.scrollAreaWidgetContents)

        self.Funtion_layout.addWidget(self.scrollArea)

        self.Funtion_layout.setStretch(0, 1)
        self.Funtion_layout.setStretch(2, 1)
        self.Funtion_layout.setStretch(4, 5)

        self.middle_layout.addLayout(self.Funtion_layout)

        self.plot3D = QFrame(main_window)
        self.plot3D.setObjectName(u"plot3D")
        self.plot3D.setStyleSheet(u"QFrame#plot3D {\n"
"    border: 2px solid  rgb(0, 0, 111); /* \u908a\u6846\u6a23\u5f0f */\n"
"}")
        self.plot3D.setFrameShape(QFrame.StyledPanel)
        self.plot3D.setFrameShadow(QFrame.Raised)
        self.horizontalLayout = QHBoxLayout(self.plot3D)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(2, 2, 2, 2)
        self.colorbar = QOpenGLWidget(self.plot3D)
        self.colorbar.setObjectName(u"colorbar")
        self.colorbar.setStyleSheet(u"")

        self.horizontalLayout.addWidget(self.colorbar)

        self.plot3D_opengl = QOpenGLWidget(self.plot3D)
        self.plot3D_opengl.setObjectName(u"plot3D_opengl")
        self.plot3D_opengl.setMaximumSize(QSize(16777215, 16777215))
        self.plot3D_opengl.setStyleSheet(u"")

        self.horizontalLayout.addWidget(self.plot3D_opengl)

        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 9)

        self.middle_layout.addWidget(self.plot3D)

        self.Fz_T_TempV_layout = QVBoxLayout()
        self.Fz_T_TempV_layout.setObjectName(u"Fz_T_TempV_layout")
        self.FzV_frame = QFrame(main_window)
        self.FzV_frame.setObjectName(u"FzV_frame")
        self.FzV_frame.setStyleSheet(u"QFrame#FzV_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));\n"
"    border: 2px solid  rgb(0, 0, 111); /* \u908a\u6846\u6a23\u5f0f */\n"
"}")
        self.TorqueV_layout_2 = QVBoxLayout(self.FzV_frame)
        self.TorqueV_layout_2.setObjectName(u"TorqueV_layout_2")
        self.TorqueV_layout_2.setContentsMargins(0, 5, 0, 0)
        self.FzH_frame = QFrame(self.FzV_frame)
        self.FzH_frame.setObjectName(u"FzH_frame")
        self.FzH_frame.setStyleSheet(u"")
        self.TorqueH_layout_2 = QHBoxLayout(self.FzH_frame)
        self.TorqueH_layout_2.setObjectName(u"TorqueH_layout_2")
        self.TorqueH_layout_2.setContentsMargins(5, 0, 5, 0)
        self.Fz_lable = QLabel(self.FzH_frame)
        self.Fz_lable.setObjectName(u"Fz_lable")
        self.Fz_lable.setFont(font)
        self.Fz_lable.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_2.addWidget(self.Fz_lable)

        self.horizontalSpacer_51 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_2.addItem(self.horizontalSpacer_51)

        self.Fz_max = QLabel(self.FzH_frame)
        self.Fz_max.setObjectName(u"Fz_max")
        self.Fz_max.setFont(font)
        self.Fz_max.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_2.addWidget(self.Fz_max)

        self.horizontalSpacer_52 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_2.addItem(self.horizontalSpacer_52)

        self.Fz_avg = QLabel(self.FzH_frame)
        self.Fz_avg.setObjectName(u"Fz_avg")
        self.Fz_avg.setFont(font)
        self.Fz_avg.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_2.addWidget(self.Fz_avg)

        self.horizontalSpacer_53 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_2.addItem(self.horizontalSpacer_53)

        self.TorqueH_layout_2.setStretch(0, 2)
        self.TorqueH_layout_2.setStretch(1, 1)
        self.TorqueH_layout_2.setStretch(2, 2)
        self.TorqueH_layout_2.setStretch(3, 1)
        self.TorqueH_layout_2.setStretch(4, 2)
        self.TorqueH_layout_2.setStretch(5, 1)

        self.TorqueV_layout_2.addWidget(self.FzH_frame)

        self.Torque_opengl_2 = QOpenGLWidget(self.FzV_frame)
        self.Torque_opengl_2.setObjectName(u"Torque_opengl_2")

        self.TorqueV_layout_2.addWidget(self.Torque_opengl_2)

        self.TorqueV_layout_2.setStretch(1, 20)

        self.Fz_T_TempV_layout.addWidget(self.FzV_frame)

        self.TorqueV_frame = QFrame(main_window)
        self.TorqueV_frame.setObjectName(u"TorqueV_frame")
        font2 = QFont()
        font2.setPointSize(9)
        self.TorqueV_frame.setFont(font2)
        self.TorqueV_frame.setStyleSheet(u"QFrame#TorqueV_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));\n"
"    border: 2px solid  rgb(0, 0, 111); /* \u908a\u6846\u6a23\u5f0f */\n"
"}")
        self.TorqueV_layout = QVBoxLayout(self.TorqueV_frame)
        self.TorqueV_layout.setObjectName(u"TorqueV_layout")
        self.TorqueV_layout.setContentsMargins(0, 5, 0, 0)
        self.TorqueH_frame = QFrame(self.TorqueV_frame)
        self.TorqueH_frame.setObjectName(u"TorqueH_frame")
        self.TorqueH_layout = QHBoxLayout(self.TorqueH_frame)
        self.TorqueH_layout.setObjectName(u"TorqueH_layout")
        self.TorqueH_layout.setContentsMargins(5, 0, 5, 0)
        self.Torque_lable = QLabel(self.TorqueH_frame)
        self.Torque_lable.setObjectName(u"Torque_lable")
        self.Torque_lable.setFont(font)
        self.Torque_lable.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout.addWidget(self.Torque_lable)

        self.horizontalSpacer_48 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout.addItem(self.horizontalSpacer_48)

        self.Torque_max = QLabel(self.TorqueH_frame)
        self.Torque_max.setObjectName(u"Torque_max")
        self.Torque_max.setFont(font)
        self.Torque_max.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout.addWidget(self.Torque_max)

        self.horizontalSpacer_49 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout.addItem(self.horizontalSpacer_49)

        self.Torque_avg = QLabel(self.TorqueH_frame)
        self.Torque_avg.setObjectName(u"Torque_avg")
        self.Torque_avg.setFont(font)
        self.Torque_avg.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout.addWidget(self.Torque_avg)

        self.horizontalSpacer_50 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout.addItem(self.horizontalSpacer_50)

        self.TorqueH_layout.setStretch(0, 2)
        self.TorqueH_layout.setStretch(1, 1)
        self.TorqueH_layout.setStretch(2, 2)
        self.TorqueH_layout.setStretch(3, 1)
        self.TorqueH_layout.setStretch(4, 2)
        self.TorqueH_layout.setStretch(5, 1)

        self.TorqueV_layout.addWidget(self.TorqueH_frame)

        self.Torque_opengl = QOpenGLWidget(self.TorqueV_frame)
        self.Torque_opengl.setObjectName(u"Torque_opengl")

        self.TorqueV_layout.addWidget(self.Torque_opengl)

        self.TorqueV_layout.setStretch(1, 20)

        self.Fz_T_TempV_layout.addWidget(self.TorqueV_frame)

        self.TempV_frame = QFrame(main_window)
        self.TempV_frame.setObjectName(u"TempV_frame")
        self.TempV_frame.setStyleSheet(u"QFrame#TempV_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));\n"
"    border: 2px solid  rgb(0, 0, 111); /* \u908a\u6846\u6a23\u5f0f */\n"
"}")
        self.TorqueV_layout_3 = QVBoxLayout(self.TempV_frame)
        self.TorqueV_layout_3.setObjectName(u"TorqueV_layout_3")
        self.TorqueV_layout_3.setContentsMargins(0, 5, 0, 0)
        self.TempH_frame = QFrame(self.TempV_frame)
        self.TempH_frame.setObjectName(u"TempH_frame")
        self.TempH_frame.setStyleSheet(u"")
        self.TorqueH_layout_3 = QHBoxLayout(self.TempH_frame)
        self.TorqueH_layout_3.setObjectName(u"TorqueH_layout_3")
        self.TorqueH_layout_3.setContentsMargins(5, 0, 5, 0)
        self.Temp_lable = QLabel(self.TempH_frame)
        self.Temp_lable.setObjectName(u"Temp_lable")
        self.Temp_lable.setFont(font)
        self.Temp_lable.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_3.addWidget(self.Temp_lable)

        self.horizontalSpacer_54 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_3.addItem(self.horizontalSpacer_54)

        self.Temp_max = QLabel(self.TempH_frame)
        self.Temp_max.setObjectName(u"Temp_max")
        self.Temp_max.setFont(font)
        self.Temp_max.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_3.addWidget(self.Temp_max)

        self.horizontalSpacer_55 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_3.addItem(self.horizontalSpacer_55)

        self.Temp_avg = QLabel(self.TempH_frame)
        self.Temp_avg.setObjectName(u"Temp_avg")
        self.Temp_avg.setFont(font)
        self.Temp_avg.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.TorqueH_layout_3.addWidget(self.Temp_avg)

        self.horizontalSpacer_56 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.TorqueH_layout_3.addItem(self.horizontalSpacer_56)

        self.TorqueH_layout_3.setStretch(0, 2)
        self.TorqueH_layout_3.setStretch(1, 1)
        self.TorqueH_layout_3.setStretch(2, 2)
        self.TorqueH_layout_3.setStretch(3, 1)
        self.TorqueH_layout_3.setStretch(4, 2)
        self.TorqueH_layout_3.setStretch(5, 1)

        self.TorqueV_layout_3.addWidget(self.TempH_frame)

        self.Torque_opengl_3 = QOpenGLWidget(self.TempV_frame)
        self.Torque_opengl_3.setObjectName(u"Torque_opengl_3")

        self.TorqueV_layout_3.addWidget(self.Torque_opengl_3)

        self.TorqueV_layout_3.setStretch(1, 20)

        self.Fz_T_TempV_layout.addWidget(self.TempV_frame)

        self.Fz_T_TempV_layout.setStretch(0, 1)
        self.Fz_T_TempV_layout.setStretch(1, 1)
        self.Fz_T_TempV_layout.setStretch(2, 1)

        self.middle_layout.addLayout(self.Fz_T_TempV_layout)

        self.middle_layout.setStretch(0, 2)
        self.middle_layout.setStretch(1, 12)
        self.middle_layout.setStretch(2, 14)

        self.verticalLayout.addLayout(self.middle_layout)

        self.Fx_Fy_CF_layout = QHBoxLayout()
        self.Fx_Fy_CF_layout.setObjectName(u"Fx_Fy_CF_layout")
        self.horizontalSpacer_22 = QSpacerItem(13, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Fx_Fy_CF_layout.addItem(self.horizontalSpacer_22)

        self.Fx_Fy_CFH_frame = QFrame(main_window)
        self.Fx_Fy_CFH_frame.setObjectName(u"Fx_Fy_CFH_frame")
        self.Fx_Fy_CFH_frame.setStyleSheet(u"QFrame#Fx_Fy_CFH_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));\n"
"    border: 2px solid  rgb(0, 0, 111); /* \u908a\u6846\u6a23\u5f0f */\n"
"}")
        self.Fx_Fy_CFH_layout = QHBoxLayout(self.Fx_Fy_CFH_frame)
        self.Fx_Fy_CFH_layout.setObjectName(u"Fx_Fy_CFH_layout")
        self.Fx_Fy_CFH_layout.setContentsMargins(0, 0, 0, 0)
        self.Fx_Fy_CFV_frame = QFrame(self.Fx_Fy_CFH_frame)
        self.Fx_Fy_CFV_frame.setObjectName(u"Fx_Fy_CFV_frame")
        self.Fx_Fy_CFV_layout = QVBoxLayout(self.Fx_Fy_CFV_frame)
        self.Fx_Fy_CFV_layout.setObjectName(u"Fx_Fy_CFV_layout")
        self.N_lable_layout = QHBoxLayout()
        self.N_lable_layout.setObjectName(u"N_lable_layout")
        self.N_lable = QLabel(self.Fx_Fy_CFV_frame)
        self.N_lable.setObjectName(u"N_lable")
        self.N_lable.setFont(font)
        self.N_lable.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.N_lable_layout.addWidget(self.N_lable)

        self.horizontalSpacer_19 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.N_lable_layout.addItem(self.horizontalSpacer_19)


        self.Fx_Fy_CFV_layout.addLayout(self.N_lable_layout)

        self.Fx_Fy_CF_btn_layout = QHBoxLayout()
        self.Fx_Fy_CF_btn_layout.setObjectName(u"Fx_Fy_CF_btn_layout")
        self.Fx_btn = QPushButton(self.Fx_Fy_CFV_frame)
        self.Fx_btn.setObjectName(u"Fx_btn")
        self.Fx_btn.setFont(font)
        self.Fx_btn.setStyleSheet(u"QPushButton {\n"
"color:#B4E100;\n"
"border:none;\n"
"}\n"
"\n"
"QPushButton:checked {\n"
"color:#666666;\n"
"border:none;\n"
"}")
        self.Fx_btn.setCheckable(True)

        self.Fx_Fy_CF_btn_layout.addWidget(self.Fx_btn)

        self.horizontalSpacer_28 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Fx_Fy_CF_btn_layout.addItem(self.horizontalSpacer_28)

        self.Fy_btn = QPushButton(self.Fx_Fy_CFV_frame)
        self.Fy_btn.setObjectName(u"Fy_btn")
        self.Fy_btn.setFont(font)
        self.Fy_btn.setStyleSheet(u"QPushButton {\n"
"color:#FFFFFF;\n"
"border:none;\n"
"}\n"
"\n"
"QPushButton:checked {\n"
"color:#666666;\n"
"border:none;\n"
"}")
        self.Fy_btn.setCheckable(True)

        self.Fx_Fy_CF_btn_layout.addWidget(self.Fy_btn)

        self.horizontalSpacer_32 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Fx_Fy_CF_btn_layout.addItem(self.horizontalSpacer_32)

        self.CF_btn = QPushButton(self.Fx_Fy_CFV_frame)
        self.CF_btn.setObjectName(u"CF_btn")
        self.CF_btn.setFont(font)
        self.CF_btn.setStyleSheet(u"QPushButton {\n"
"color:#00FFFF;\n"
"border:none;\n"
"}\n"
" \n"
"QPushButton:checked {\n"
"color:#666666;\n"
"border:none;\n"
"}")
        self.CF_btn.setCheckable(True)

        self.Fx_Fy_CF_btn_layout.addWidget(self.CF_btn)

        self.horizontalSpacer_17 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Fx_Fy_CF_btn_layout.addItem(self.horizontalSpacer_17)

        self.Fx_Fy_CF_btn_layout.setStretch(0, 1)
        self.Fx_Fy_CF_btn_layout.setStretch(1, 1)
        self.Fx_Fy_CF_btn_layout.setStretch(2, 1)
        self.Fx_Fy_CF_btn_layout.setStretch(3, 1)
        self.Fx_Fy_CF_btn_layout.setStretch(4, 1)
        self.Fx_Fy_CF_btn_layout.setStretch(5, 5)

        self.Fx_Fy_CFV_layout.addLayout(self.Fx_Fy_CF_btn_layout)

        self.verticalSpacer_10 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.Fx_Fy_CFV_layout.addItem(self.verticalSpacer_10)

        self.CF_max = QLabel(self.Fx_Fy_CFV_frame)
        self.CF_max.setObjectName(u"CF_max")
        self.CF_max.setFont(font)
        self.CF_max.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.Fx_Fy_CFV_layout.addWidget(self.CF_max)

        self.CF_avg = QLabel(self.Fx_Fy_CFV_frame)
        self.CF_avg.setObjectName(u"CF_avg")
        self.CF_avg.setFont(font)
        self.CF_avg.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.Fx_Fy_CFV_layout.addWidget(self.CF_avg)

        self.verticalSpacer_6 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.Fx_Fy_CFV_layout.addItem(self.verticalSpacer_6)

        self.Fx_Fy_CFV_layout.setStretch(2, 15)
        self.Fx_Fy_CFV_layout.setStretch(5, 1)

        self.Fx_Fy_CFH_layout.addWidget(self.Fx_Fy_CFV_frame)

        self.Fx_Fy_CF_opengl = QOpenGLWidget(self.Fx_Fy_CFH_frame)
        self.Fx_Fy_CF_opengl.setObjectName(u"Fx_Fy_CF_opengl")

        self.Fx_Fy_CFH_layout.addWidget(self.Fx_Fy_CF_opengl)

        self.Fx_Fy_CFH_layout.setStretch(0, 15)
        self.Fx_Fy_CFH_layout.setStretch(1, 120)

        self.Fx_Fy_CF_layout.addWidget(self.Fx_Fy_CFH_frame)

        self.Fx_Fy_CF_layout.setStretch(0, 1)
        self.Fx_Fy_CF_layout.setStretch(1, 100)

        self.verticalLayout.addLayout(self.Fx_Fy_CF_layout)

        self.bottom_frame = QFrame(main_window)
        self.bottom_frame.setObjectName(u"bottom_frame")
        self.bottom_frame.setStyleSheet(u"QFrame#bottom_frame {\n"
"	background-color: qlineargradient(spread:pad, x1:0, y1:1, x2:0, y2:0, stop:0 rgba(0, 0, 127, 141), stop:1 rgba(0, 0, 0, 72));\n"
"}")
        self.Bottom = QHBoxLayout(self.bottom_frame)
        self.Bottom.setObjectName(u"Bottom")
        self.horizontalSpacer_21 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Bottom.addItem(self.horizontalSpacer_21)

        self.Label_Version = QLabel(self.bottom_frame)
        self.Label_Version.setObjectName(u"Label_Version")
        self.Label_Version.setFont(font)
        self.Label_Version.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.Bottom.addWidget(self.Label_Version)

        self.horizontalSpacer_18 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Bottom.addItem(self.horizontalSpacer_18)

        self.Label_Info = QLabel(self.bottom_frame)
        self.Label_Info.setObjectName(u"Label_Info")
        self.Label_Info.setFont(font)
        self.Label_Info.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.Bottom.addWidget(self.Label_Info)

        self.horizontalSpacer_20 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Bottom.addItem(self.horizontalSpacer_20)

        self.Image_machradar = QPushButton(self.bottom_frame)
        self.Image_machradar.setObjectName(u"Image_machradar")
        self.Image_machradar.setFont(font1)
        self.Image_machradar.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")
        icon11 = QIcon()
        icon11.addFile(u":/other/logo_machradarpro.png", QSize(), QIcon.Normal, QIcon.Off)
        self.Image_machradar.setIcon(icon11)
        self.Image_machradar.setIconSize(QSize(255, 15))

        self.Bottom.addWidget(self.Image_machradar)

        self.Bottom.setStretch(0, 1)
        self.Bottom.setStretch(1, 3)
        self.Bottom.setStretch(2, 1)
        self.Bottom.setStretch(3, 3)
        self.Bottom.setStretch(4, 20)
        self.Bottom.setStretch(5, 3)

        self.verticalLayout.addWidget(self.bottom_frame)

        self.verticalLayout.setStretch(1, 2)
        self.verticalLayout.setStretch(2, 1)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)

        self.retranslateUi(main_window)

        self.Btn1_Tare.setDefault(False)


        QMetaObject.connectSlotsByName(main_window)
    # setupUi

    def retranslateUi(self, main_window):
        main_window.setWindowTitle(QCoreApplication.translate("main_window", u"MachRadarPro", None))
        self.Btn_Setting.setText(QCoreApplication.translate("main_window", u"\u8a2d\u5b9a", None))
        self.Btn_Filter.setText(QCoreApplication.translate("main_window", u"\u6ffe\u6ce2", None))
        self.Btn_View.setText(QCoreApplication.translate("main_window", u"\u986f\u793a", None))
        self.Btn_Event.setText(QCoreApplication.translate("main_window", u"\u4e8b\u4ef6", None))
        self.Btn_Mode.setText(QCoreApplication.translate("main_window", u"\u6a21\u5f0f", None))
        self.Btn_RecordScript.setText(QCoreApplication.translate("main_window", u"\u8173\u672c", None))
        self.Btn_Help.setText(QCoreApplication.translate("main_window", u"\u8aaa\u660e", None))
        self.lable.setText(QCoreApplication.translate("main_window", u"|", None))
        self.Btn_Toolname.setText(QCoreApplication.translate("main_window", u"<\u667a\u6167\u5200\u628a>", None))
        self.Label_ECO.setText(QCoreApplication.translate("main_window", u"0.0 g CO2e", None))
        self.Btn_CO2.setText("")
        self.wifi_rssi.setText(QCoreApplication.translate("main_window", u"-0dBm", None))
        self.WIFI.setText("")
        self.Battery.setText("")
        self.Btn_Link.setText("")
        self.Btn_Record.setText("")
        self.Btn1_Tare.setText("")
        self.Btn2_BGMode.setText("")
        self.Btn3_PlotMode.setText("")
        self.Btn4_ResetAngle.setText("")
        self.Btn5_cnc_Link.setText("")
        self.Btn6_angle1.setText("")
        self.Btn7_sleep_mode.setText("")
        self.Fz_lable.setText(QCoreApplication.translate("main_window", u"Fz [N]", None))
        self.Fz_max.setText(QCoreApplication.translate("main_window", u"Max : 0", None))
        self.Fz_avg.setText(QCoreApplication.translate("main_window", u"Avg : 0", None))
        self.Torque_lable.setText(QCoreApplication.translate("main_window", u"Torque [Nm]", None))
        self.Torque_max.setText(QCoreApplication.translate("main_window", u"Max : 0", None))
        self.Torque_avg.setText(QCoreApplication.translate("main_window", u"Avg : 0", None))
        self.Temp_lable.setText(QCoreApplication.translate("main_window", u"\u6eab\u5ea6 [\u00b0C]", None))
        self.Temp_max.setText(QCoreApplication.translate("main_window", u"Max : 0", None))
        self.Temp_avg.setText(QCoreApplication.translate("main_window", u"Avg : 0", None))
        self.N_lable.setText(QCoreApplication.translate("main_window", u"[N]", None))
        self.Fx_btn.setText(QCoreApplication.translate("main_window", u"Fx", None))
        self.Fy_btn.setText(QCoreApplication.translate("main_window", u"Fy", None))
        self.CF_btn.setText(QCoreApplication.translate("main_window", u"C.F.", None))
        self.CF_max.setText(QCoreApplication.translate("main_window", u"Max : 0", None))
        self.CF_avg.setText(QCoreApplication.translate("main_window", u"Avg  : 0", None))
        self.Label_Version.setText(QCoreApplication.translate("main_window", u"\u7248\u672c1.03.603.121", None))
        self.Label_Info.setText(QCoreApplication.translate("main_window", u"\u9ede\u72c0\u6a21\u5f0f", None))
        self.Image_machradar.setText("")
    # retranslateUi

