from PySide2.QtCore import QThread, Signal
import socket
import time
import math
from utils.cirular_queue import CircularQueue
from . import logger  # 從同一個包導入 logger

"""
SocketWorker 自動重連功能使用說明：

1. 基本使用：
   worker = SocketWorker(host='*************', port=1333)
   worker.start()

2. 設置自動重連：
   worker.set_auto_reconnect(True, interval=5, max_attempts=10)
   # 啟用自動重連，間隔5秒，最多重連10次

3. 設置連線超時：
   worker.set_connection_timeout(15)  # 15秒連線超時

4. 設置心跳檢測：
   worker.set_heartbeat_interval(30)  # 30秒無數據則認為連線異常

5. 強制重連：
   worker.force_reconnect()

6. 獲取連線狀態：
   status = worker.get_connection_status()

7. 監聽連線狀態變化：
   worker.sig_connection_status.connect(your_status_handler)
   worker.sig_socket_connect.connect(your_connect_handler)
"""

class SocketWorker(QThread):
    # raw_data_received = Signal(str)  # 當收到數據時發出 Signal
    raw_data_received = Signal(str)  # 發送原始數據信號
    sig_socket_connect = Signal(object)  # 發送 Socket 信號
    sig_connection_status = Signal(str)  # 發送連線狀態信號

    def __init__(self, host='*************', port=1333,sampleRate=10000):
        super().__init__()
        self.host = host
        self.port = port
        self.SampleRate = sampleRate # 10000  # 取資料
        self.SamplePoint1 = math.ceil(self.SampleRate / 12.5)
        self.sample_N = math.ceil(self.SamplePoint1 * 1.005)
        self.sample_byte = int(self.sample_N * 16)

        self.running = False
        self.paused = False  # **新增：控制是否暫停數據接收**
        self.sock = None

        # 重連相關參數
        self.auto_reconnect = True  # 是否啟用自動重連
        self.reconnect_interval = 5  # 重連間隔（秒）
        self.max_reconnect_attempts = 10  # 最大重連次數，0表示無限重連
        self.reconnect_count = 0  # 當前重連次數
        self.connection_timeout = 10  # 連線超時時間（秒）
        self.heartbeat_interval = 30  # 心跳檢測間隔（秒）
        self.last_data_time = time.time()  # 最後收到數據的時間
        self.is_connected = False  # 連線狀態

        logger.info("SocketWorker Init")

        # self.data_queue = CircularQueue(maxsize=10)
        self.collect_data = ""  # 存放累積的數據
        self.temp_data = ""  # 存放不滿 32-byte 的數據

    def __del__(self):
        logger.error(f"SocketWorker {self.host} 已被銷毀")

    def run(self):
        """ 執行緒執行的 socket 讀取邏輯，支援自動重連 """
        logger.info("SocketWorker RUN")
        self.running = True
        self.reconnect_count = 0

        while self.running:
            try:
                # 嘗試連線
                if not self.connect_to_server():
                    if not self.auto_reconnect or (self.max_reconnect_attempts > 0 and self.reconnect_count >= self.max_reconnect_attempts):
                        logger.error("達到最大重連次數或禁用自動重連，停止嘗試")
                        break
                    continue

                # 連線成功，開始數據接收循環
                self.data_receive_loop()

            except Exception as e:
                logger.error(f"SocketWorker 運行時發生異常: {e}")
                self.handle_connection_error()

        # 清理資源
        self.close_socket()
        logger.info("SocketWorker 已停止")
        data = {"status": False, "host": self.host}
        self.sig_socket_connect.emit(data)
        self.exec_()

    def connect_to_server(self):
        """嘗試連接到服務器"""
        try:
            if self.sock:
                self.close_socket()

            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            self.sock.settimeout(self.connection_timeout)

            logger.info(f"嘗試連接到 {self.host}:{self.port} (第 {self.reconnect_count + 1} 次)")
            self.sock.connect((self.host, self.port))

            # 連線成功
            self.sock.settimeout(3)  # 設置數據接收超時
            self.is_connected = True
            self.reconnect_count = 0  # 重置重連計數
            self.last_data_time = time.time()

            logger.info(f"成功連接到 {self.host}:{self.port}")
            self.sig_connection_status.emit("已連接")
            data = {"status": True, "host": self.host}
            self.sig_socket_connect.emit(data)

            return True

        except socket.error as e:
            logger.error(f"連接失敗: {e}")
            self.is_connected = False
            self.reconnect_count += 1
            self.sig_connection_status.emit(f"連接失敗 (第 {self.reconnect_count} 次)")

            if self.auto_reconnect and (self.max_reconnect_attempts == 0 or self.reconnect_count < self.max_reconnect_attempts):
                logger.info(f"將在 {self.reconnect_interval} 秒後重試...")
                time.sleep(self.reconnect_interval)

            return False

    def data_receive_loop(self):
        """數據接收循環"""
        while self.running and self.is_connected:
            if self.paused:
                logger.debug("SocketWorker 暫停接收數據...")
                time.sleep(0.5)
                continue

            try:
                # 檢查連線狀態
                if not self.check_connection_health():
                    logger.warning("連線健康檢查失敗，嘗試重連")
                    break

                if self.sock is None:
                    logger.error("Socket 已關閉，停止讀取")
                    break

                hex_data = self.receive_chunk(self.sample_N, self.sample_byte)
                if hex_data:
                    self.emit_data(hex_data)
                    self.last_data_time = time.time()  # 更新最後收到數據的時間

            except socket.timeout:
                logger.warning("數據接收超時，檢查連線狀態")
                if not self.check_connection_health():
                    break
                continue
            except socket.error as e:
                logger.error(f"Socket Error: {e}")
                self.handle_connection_error()
                break

    def check_connection_health(self):
        """檢查連線健康狀態"""
        current_time = time.time()

        # 檢查是否長時間沒有收到數據
        if current_time - self.last_data_time > self.heartbeat_interval:
            logger.warning(f"超過 {self.heartbeat_interval} 秒未收到數據，可能連線異常")
            return False

        # 可以添加其他健康檢查邏輯
        return True

    def handle_connection_error(self):
        """處理連線錯誤"""
        self.is_connected = False
        self.sig_connection_status.emit("連線中斷")
        data = {"status": False, "host": self.host}
        self.sig_socket_connect.emit(data)

        if self.auto_reconnect:
            logger.info("準備自動重連...")
        else:
            logger.info("自動重連已禁用")

    def pause(self):
        """暫停數據接收，但不關閉 Socket"""
        logger.info("暫停數據接收...")
        self.paused = True

    def resume(self):
        """恢復數據接收"""
        logger.info("恢復數據接收...")
        self.paused = False

    def set_auto_reconnect(self, enabled, interval=5, max_attempts=10):
        """設置自動重連參數

        Args:
            enabled (bool): 是否啟用自動重連
            interval (int): 重連間隔（秒）
            max_attempts (int): 最大重連次數，0表示無限重連
        """
        self.auto_reconnect = enabled
        self.reconnect_interval = interval
        self.max_reconnect_attempts = max_attempts
        logger.info(f"自動重連設置: 啟用={enabled}, 間隔={interval}秒, 最大次數={max_attempts}")

    def set_connection_timeout(self, timeout):
        """設置連線超時時間"""
        self.connection_timeout = timeout
        logger.info(f"連線超時設置為 {timeout} 秒")

    def set_heartbeat_interval(self, interval):
        """設置心跳檢測間隔"""
        self.heartbeat_interval = interval
        logger.info(f"心跳檢測間隔設置為 {interval} 秒")

    def force_reconnect(self):
        """強制重連"""
        logger.info("強制重連...")
        self.is_connected = False
        self.reconnect_count = 0
        if self.sock:
            self.close_socket()

    def get_connection_status(self):
        """獲取連線狀態信息"""
        return {
            "is_connected": self.is_connected,
            "host": self.host,
            "port": self.port,
            "reconnect_count": self.reconnect_count,
            "auto_reconnect": self.auto_reconnect,
            "last_data_time": self.last_data_time
        }

    def stop(self):
        """ 安全停止執行緒 """
        logger.info("SocketWorker 正在停止...")
        self.running = False
        self.auto_reconnect = False  # 停止自動重連
        self.is_connected = False
        self.close_socket()

        self.quit()  # ✅ 正確終止事件迴圈
        if not self.wait(1000):  # ✅ 最多等待 1 秒，避免 UI 當機
            logger.warning("SocketWorker 未能正常結束，強制終止")
            self.terminate()  # 🚨 最後手段，強制終止
            self.wait()  # 確保完全停止

    def start_client(self):
        try:
            logger.info(f"start_client {self.host} 連線中")
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            self.running = True
        except socket.error as e:
            # print(f"Error starting client: {e}")
            logger.error(f"Error starting client: {e}")
            self.stop()

    def close_socket(self):
        """ 安全關閉 socket，避免錯誤 """
        if self.sock:
            try:
                logger.info("關閉 socket 連線...")
                self.sock.shutdown(socket.SHUT_RDWR)  # ✅ 避免 `WinError 10038`
            except Exception as e:
                logger.warning(f"Socket 關閉異常: {e}")
            finally:
                self.sock.close()
                self.sock = None  # ✅ 確保 socket 變數不再使用
                logger.info("Socket 已關閉")

    def send_sleepmode(self,is_sleep_on):
        if not is_sleep_on: #睡眠未開啟
            self.sock.send("sleep_on\n".encode())
            logger.error("sleep_on")
        else: #睡眠開啟
            self.sock.send("sleep_off\n".encode())
            logger.error("sleep_off")

    def process_data(self, hex_data):
        """負責整理數據，每 32-byte 為一組"""
    
        # 累積數據
        if self.temp_data:
            hex_data = self.temp_data + hex_data  # 加上上次未處理的數  據
    
        N = len(hex_data) // 64  # 計算完整的 32-byte 區塊數量
        complete_data = hex_data[:N*64]  # 取出完整的 32-byte 數據
        self.temp_data = hex_data[N*64:]  # 更新未處理的部分
    
        return complete_data  # 回傳完整數據

    def emit_data(self, hex_data):
        """負責發送數據到主執行緒"""
        if hex_data:
            # logger.debug(f"發送數據: {hex_data[:50]}... (共 {len(hex_data)} HEX 字符)")
            self.raw_data_received.emit(hex_data)  # 發送完整的 32-byte 數據

    # 刀把資料收集-持續接收資料，直到接收完整的一段資料後回傳
    def receive_chunk(self, sample_N , sample_byte):
        # print("client 取資料中...")
        # logger.debug("client 取資料中...")
        count_get = 0
        # sample_byte = int(sample_N * 16)
        collect_data = ""
        self.temp_data = ""

        try:
            if not self.running or not self.sock:
                raise Exception("Client is not running or socket is invalid.")
            
            while count_get < sample_N:
                data_recv = self.sock.recv(sample_byte).hex()
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if self.temp_data:
                    data_recv = self.temp_data + data_recv

                N = len(data_recv) // 32
                count_get += N

                if len(data_recv) % 32 > 0:
                    self.temp_data = data_recv[N*32:]
                else:
                    self.temp_data = []

                # ✅ 避免計算錯誤
                remaining_samples = sample_N - count_get
                sample_byte = max((remaining_samples * 32 - len(self.temp_data)) // 2, 1)

            # print("client 取資料完...")
            # logger.debug("client 取資料完...")
            return collect_data

        except socket.error as e:
            logger.error(f"Socket error in receive_chunk: {e}")
            self.is_connected = False
            raise


    

    # def run(self):
    #     """ 執行緒執行的 socket 讀取邏輯 """
    #     self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    #     self.sock.connect((self.host, self.port))
    #     self.sock.settimeout(1)  # 設定超時，避免阻塞

    #     while self.running:
    #         try:
    #             data = self.sock.recv(1024)
    #             if data:
    #                 self.data_received.emit(data.decode())  # 發送數據到主執行緒
    #         except socket.timeout:
    #             continue  # 忽略超時錯誤，繼續監聽
    #         except Exception as e:
    #             print(f"Socket Error: {e}")
    #             break
        
    #     self.sock.close()
