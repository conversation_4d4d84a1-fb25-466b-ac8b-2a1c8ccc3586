import numpy as np
from scipy import signal
from scipy.signal import savgol_filter
import pandas as pd
from . import logger  # 導入 logger

# TODO: 把它移動到 decoder_Worker 暫時無實現 Filter 類 有問題
class FilterData:
    def __init__(self):
        """
        初始化濾波器
        :param filter_type: 濾波類型 ('moving_average' 或 'ema')
        :param window_size: 移動平均窗口大小（適用於移動平均濾波）
        :param alpha: 平滑係數（適用於指數移動平均濾波）
        """
        # self.filter_type = filter_type
       
        self.data = []
        self.ema_value = None

    def apply(self, new_value):
        """
        應用濾波器
        :param new_value: 新的數據值
        :return: 濾波後的數據值
        """
        if self.filter_type == 'moving_average':
            return self._moving_average(new_value)
        elif self.filter_type == 'ema':
            return self._exponential_moving_average(new_value)
        else:
            raise ValueError("Unsupported filter type")

    def Moving_average(self, new_value, moving_average):
        """ 移動平均濾波 
        :param new_value: 新的數據值
        :param moving_average: 移動平均窗口大小
        :return: 濾波後的數據值
        """

        Avg_Bending = pd.Series(new_value)
        Avg_value = Avg_Bending.rolling(window=moving_average, min_periods=1).mean()

        return Avg_value

    #---濾波事件---
    def Mean_filter (self, x, k):
        """
        均值濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數

        Apply a length-k mean filter to a 1D array x.
        Boundaries are extended by repeating endpoints.
        """
        assert k % 2 == 1, "Median filter length must be odd."
        assert x.ndim == 1, "Input must be one-dimensional."

        k2 = (k - 1) // 2
        y = np.zeros ((len (x), k), dtype=x.dtype)
        y[:,k2] = x

        for i in range (k2):
            j = k2 - i
            y[j:,i] = x[:-j]
            y[:j,i] = x[0]
            y[:-j,-(i+1)] = x[j:]
            y[-j:,-(i+1)] = x[-1]
        return np.mean (y, axis=1) #.tolist() # AIE +++ Serena [2023/10/23 list -> array]

    def Median_filter (self, x, k):
        """
        中值濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數

        Apply a length-k median filter to a 1D array x.
        Boundaries are extended by repeating endpoints.
        """
        assert k % 2 == 1, "Median filter length must be odd."
        assert x.ndim == 1, "Input must be one-dimensional."

        k2 = (k - 1) // 2
        y = np.zeros ((len (x), k), dtype=x.dtype)
        y[:,k2] = x
        for i in range (k2):
            j = k2 - i
            y[j:,i] = x[:-j]
            y[:j,i] = x[0]
            y[:-j,-(i+1)] = x[j:]
            y[-j:,-(i+1)] = x[-1]
        return np.median (y, axis=1) 

    def Gaussian_filter(self, x, size, sigma):
        """
        高斯濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數
        :param sigma: 1  預設 1
        """

        if size % 2 == 0:
            size += 1  # 將 size 設置為奇數

        filter_range = np.linspace(-int(size / 2), int(size / 2), size)
        gaussian_filter = [1 / (sigma * np.sqrt(2*np.pi)) * np.exp(-i**2 / (2*sigma**2)) for i in   filter_range]  #做高斯filter
        x = np.concatenate((np.repeat(x[0], int(size / 2)), x, np.repeat(x[-1], int(size / 2))))

        return np.convolve(x, gaussian_filter, mode='valid') #.tolist()  # AIE +++ Serena [2023/10/ 23 list -> array]

    def MS_Filter(self, X,Filter_Size,Diff_Value,Result_Value,Sample_Rate):
        """
        Machsync濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param Filter_Size: filter_Slider = 舊版 K值變數
        :param Diff_Value: 0.003
        :param Result_Value: 10
        :param Sample_Rate: SampleRate

        """

        Result_List = self.Median_filter(np.array(X),Filter_Size)
        Result_List = self.Mean_filter(np.array(Result_List),Filter_Size)
        MS_Sample_Rate=Sample_Rate

        diff_X=[]
        diff_time=[]
        index=0

        for i in range(0,len(Result_List)-1):
            diff_X.append( abs(Result_List[i]-Result_List[i+1] ) )

            diff_time.append(index)
            index+=1
        # mean_diff_X = round(np.mean(diff_X),3)

        tolerance_count=0
        test_start=False
        threshold_value=max(diff_X)/2
        Result_Value= round(max(X, key=abs)/Result_Value,3)

        for i in range(0,len(diff_X)):
            if (diff_X[i] > threshold_value and diff_X[i]>Diff_Value) or Result_List[i] >=Result_Value :  #0.01  or X[i]>=0.1   or  (diff_X[i] > mean_diff_X and     mean_diff_X!=0)

                tolerance_count=MS_Sample_Rate*0.05  #保留0.05s資料
                Result_List[i] = X[i]  #保留原資料

                if test_start==False:  #第一次超越起伏，保留前0.1s資料
                    temp_i = int(i - tolerance_count) if i - tolerance_count>=0 else 0  #以防超越第 0筆資料
                    Result_List[temp_i:i] = X[temp_i:i] 

                test_start=True
            else:
                if tolerance_count>0:
                    tolerance_count-=1
                    Result_List[i] = X[i]  #保留原資料
                else:
                    test_start=False

        return Result_List

    def Butterworth_filter(self, x, cutoff_freq, fs, btype , order=4):
        
        """
        應用Butterworth濾波器
        # 低通濾波 low / 高通濾波 high / 帶通濾波 band
        :param x: MS_BendingX Y Z T 之一的數據
        :param cutoff_freq: 截止頻率 (Hz)
        :param fs: 採樣頻率 (Hz)
        :param btype: low / 濾波器類型 ('low', 'high', 'band')
        :param order: 濾波器階數 SampleRate
        
        """
        logger.error(btype)
        logger.error(type(btype))
        from scipy import signal
        nyquist = fs #/ 2
        normalized_cutoff_freq = cutoff_freq / nyquist
        b, a = signal.butter(order, normalized_cutoff_freq, btype)
        return signal.filtfilt(b, a, x)

    def Savitzky_Golay_filter(self, x, window_length, polyorder):
        """
        應用Savitzky-Golay濾波器

        :param x: MS_BendingX Y Z T 之一的數據
        :param window_length: WL / 窗口長度（必須是奇數） 
        :param polyorder: 3 / 多項式階數
        """
        if window_length % 2 == 0:
            window_length += 1
        return savgol_filter(x, window_length, polyorder)


# 測試
# if __name__ == "__main__":
#     values = [10, 20, 30, 40, 50, 60]
    
#     print("Moving Average Filter:")
#     ma_filter = FilterData(filter_type='moving_average', window_size=3)
#     for v in values:
#         print(f"Input: {v}, Filtered: {ma_filter.apply(v)}")
    
#     print("\nExponential Moving Average Filter:")
#     ema_filter = FilterData(filter_type='ema', alpha=0.2)
#     for v in values:
#         print(f"Input: {v}, Filtered: {ema_filter.apply(v)}")
