from PySide2.QtCore import QObject, QThread, Signal, QTimer
import time
import math
import numpy as np
from scipy.signal import savgol_filter
from utils.text_file_manager import TextFileManager
from utils.cirular_queue import CircularQueue
from utils.auto_recording_manager import AutoRecordingManager
from app.models.co2_data import CO2Data
import pandas as pd
from config import DEFAULT_CO2_DATA, DEFAULT_FILTER
from . import logger  # 導入 logger

class DecoderWorker(QObject):  # 改成 QObject
    decoded_data = Signal(object)  # 解析後的數據信號
    update_record_state = Signal(bool)  # 新增用來更新 record_state 的信號
    

    def __init__(self,holder_data):
        super().__init__()

        self.data = {}

        self.current_holder_data = holder_data
        self.tare_state = False
        self.paused = False # 新增「暫停」狀態

        # TDynamic Tare 狀態
        self.Dynamic_Tare = False
        self.Dynamic_Tare_Count=[0,False] # 動態tare 計數器歸零[計數器,紀錄(T/F)]
        self.Dynamic_Tare_BX = self.current_holder_data["tare_xv"]
        self.Dynamic_Tare_BY = self.current_holder_data["tare_yv"]
        self.Dynamic_Tare_BZ = self.current_holder_data["tare_zv"]
        self.Dynamic_Tare_BT = self.current_holder_data["tare_tv"]

        # 錄製狀態
        self.record_state  = False
        self.directory = r"C:\Record"  # 指定目錄名稱
        self.filename = "my_text_file.txt"
        self.rawdata_filename = "my_rawdata_file.txt"

        self.count_second=0
        # self.sample_N=160800
        # self.sample_index=160800//16  #資料每秒最多筆數

        self.SampleRate = int(self.current_holder_data["sample_rate"]) # 取資料
        self.SamplePoint1=math.ceil(self.SampleRate/12.5)  # 每一秒取5次
        self.sample_N=math.ceil(self.SamplePoint1 *1.005)
        self.sample_byte = int(self.sample_N * 16) 
        self.SamplePoint2=self.SamplePoint1//200  #每200筆資料1筆特殊
        self.sample_index=self.sample_N//16  #資料每秒最多筆數

        self.Tare_BX = self.current_holder_data["tare_xv"]
        self.Tare_BY = self.current_holder_data["tare_yv"]
        self.Tare_BZ = self.current_holder_data["tare_zv"]
        self.Tare_BT = self.current_holder_data["tare_tv"]

        # 剛性測試機 Lc = 0.136 m 客戶輸入伸出長 Lt = 0.166 m = 0.126 m + 0.04 m(伸出長)
        # TAG: 這裡先算出 N 換算係數
        self.parm_H_L = self.current_holder_data["Lc"] / (self.current_holder_data["Hl"] + self.current_holder_data["Kl"])
        self.parm_N_X =  self.current_holder_data["Linear_x"] * self.parm_H_L
        self.parm_N_Y =  self.current_holder_data["Linear_y"] * self.parm_H_L
        self.parm_N_Z =  self.current_holder_data["Linear_z"]
        self.parm_N_T =  self.current_holder_data["Linear_t"]

        logger.error(f"Tare_BX: {self.Tare_BX} Tare_BY: {self.Tare_BY} Tare_BZ:{self.Tare_BZ} Tare_BT:{self.Tare_BT}")
        logger.error(f"parm_N_X: {self.parm_N_X} parm_N_Y: {self.parm_N_Y} parm_N_Z:{self.parm_N_Z} parm_N_T:{self.parm_N_T}")

        # 初始化為0或False，避免未賦值時回傳None造成type error
        self.temp_RSSI = 0 # Wifi訊號
        self.temp_battery = 0 # 電池電壓
        self.Charging_Flag_temp = False # 充電狀態
        self.sleep_mode_temp = False # 睡眠模式
        self.temp_temperature = 0 # 溫度

        self.MAC = None # MAC位址

        self.temp_ax = None
        self.temp_ay = None
        self.temp_az = None
        self.temp_bx = None
        self.temp_by = None
        self.temp_ten = None
        self.temp_tor = None
        self.MS_ADXL_X = None
        self.MS_ADXL_Y = None
        self.MS_ADXL_Z = None
        self.MS_BendingX = None
        self.MS_BendingY = None
        self.MS_BendingXY = None
        self.MS_Tension = None
        self.MS_Torsion = None
        self.Tare_GX = self.current_holder_data["tare_gx"]
        self.Tare_GY = self.current_holder_data["tare_gy"]
        self.Tare_GZ = self.current_holder_data["tare_gz"]
        self.txt_gx = 0
        self.AutoTare = True

        self.running = False 
        self.data_queue = CircularQueue(maxsize=10)
        self.timer = QTimer()  # 新增 QTimer
        self.timer.timeout.connect(self.process_data)  # 定期處理數據
        self.update_record_state.connect(self.set_record_state) # 連接信號到槽
        # self.update_filter_state.connect(self.set_filter_state) # 連接信號到槽

        self.filter_data = DEFAULT_FILTER.copy()

        self.CO2_id = self.current_holder_data["CO2_id"]
        self.co2instance = CO2Data()
        self.CO2_data = dict(DEFAULT_CO2_DATA)

        self.display_decimal_places = 3  # 顯示小數點位數

        self.save_rawdatafile = False

        # 手動錄製檔案 manager
        self.record_decimal_places = 6
        self.msra_file_manager = None
        self.msra_rawdatafile_manager = None
        
        # Auto recording manager
        self.auto_recording_manager = AutoRecordingManager()
        self.auto_recording_manager.auto_record_triggered.connect(self._on_auto_record_triggered)
        self.auto_recording_manager.auto_record_stopped.connect(self._on_auto_record_stopped)
        
        # Configure auto recording with current holder data
        auto_recording_settings = {
            'auto_record_enabled': bool(self.current_holder_data.get('auto_record_enabled', 0)),
            'auto_pre_record_seconds': self.current_holder_data.get('auto_pre_record_seconds', 0),
            'auto_record_seconds': self.current_holder_data.get('auto_record_seconds', 10),
            'auto_max_record_count': self.current_holder_data.get('auto_max_record_count', 100),
            'sample_rate': self.SampleRate,
            'auto_cf_threshold': self.current_holder_data.get('auto_cf_threshold', 0.0),
            'auto_fz_threshold': self.current_holder_data.get('auto_fz_threshold', 0.0),
            'auto_t_threshold': self.current_holder_data.get('auto_t_threshold', 0.0),
            'auto_cf_enabled': bool(self.current_holder_data.get('auto_cf_enabled', 0)),
            'auto_fz_enabled': bool(self.current_holder_data.get('auto_fz_enabled', 0)),
            'auto_t_enabled': bool(self.current_holder_data.get('auto_t_enabled', 0))
        }
        self.auto_recording_manager.configure(auto_recording_settings)
        
        # Set auto recording file settings
        self.auto_recording_manager.set_file_settings(self.directory, "auto_record")

    def update_msra_file_data(self, directory, file_name):
        self.directory = directory
        self.filename = file_name
        self.rawdata_filename = "RawData_" + file_name

    def set_record_state(self, state):
        print(f"更新 record_state: {state}")
        self.record_state = state

        # 手動錄製檔案建立
        if self.record_state:
            self.create_msra_file()

        '''
        # 手動錄製檔案關閉
        if not self.record_state:
            self.close_msra_file()
        '''

    def create_msra_file(self):

        tool_info_data = f"!,{self.current_holder_data['sample_rate']},{round(self.current_holder_data['tare_xv'],self.record_decimal_places)},{round(self.current_holder_data['tare_yv'],self.record_decimal_places)},{round(self.current_holder_data['tare_zv'],self.record_decimal_places)},{round(self.current_holder_data['tare_tv'],self.record_decimal_places)},{self.current_holder_data['Linear_x']},{self.current_holder_data['Linear_y']},{self.current_holder_data['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{self.current_holder_data['Lc']},{self.current_holder_data['Hl']},{self.current_holder_data['Kl']},{self.current_holder_data['Bx_COMP']},{self.current_holder_data['By_COMP']},{self.current_holder_data['Bz_COMP']},{self.current_holder_data['Bt_COMP']}\n"
        logger.debug(f"tool_info_data: {tool_info_data}")

        logger.info(f"手動錄製檔案建立: {self.directory} {self.filename}")
        self.msra_file_manager = TextFileManager(self.directory)
        self.msra_file_manager.create_file(self.filename)
        self.msra_file_manager.add_text(self.filename, tool_info_data)

        if self.save_rawdatafile:
            logger.info(f"raw data create: {self.directory} {self.rawdata_filename}")
            self.msra_rawdatafile_manager = TextFileManager(self.directory)
            self.msra_rawdatafile_manager.create_file(self.rawdata_filename)
            self.msra_rawdatafile_manager.add_text(self.rawdata_filename, tool_info_data)

        logger.info("create_msra_file 完成")

    '''
    # TODO: 優化TextFileManager寫檔方式，在這裡關閉檔案
    def close_msra_file(self):
        logger.info(f"close_msra_file: {self.directory} {self.filename}")
        if self.msra_file_manager is not None:
            self.msra_file_manager.close_file()
            self.msra_file_manager = None

        if self.save_rawdatafile and self.msra_rawdatafile_manager is not None:
            logger.info(f"close_msra_rawdatafile: {self.directory} {self.rawdata_filename}")
            self.msra_rawdatafile_manager.close_file()
            self.msra_rawdatafile_manager = None
    '''
    
    def configure_auto_recording(self, settings):
        """Configure auto recording settings"""
        self.auto_recording_manager.configure(settings)
        self.auto_recording_manager.set_file_settings(self.directory, "auto_record")
        
    def set_auto_recording_file_settings(self, directory, filename):
        """Set file directory and filename for auto recording"""
        self.auto_recording_manager.set_file_settings(directory, filename)
        
    def _on_auto_record_triggered(self, trigger_reason):
        """Handle auto recording trigger event"""
        logger.info(f"Auto recording triggered: {trigger_reason}")
        # The auto recording manager handles its own file creation and writing
        
    def _on_auto_record_stopped(self):
        """Handle auto recording stop event"""
        logger.info("Auto recording stopped")
        
    def _process_auto_recording(self, sensor_data, data_line):
        """Process auto recording logic"""
        # Check for triggers
        if self.auto_recording_manager.check_triggers(sensor_data):
            # Trigger detected, auto recording manager will handle the rest
            pass
        
        # Process data point for pre-recording buffer and active recording
        self.auto_recording_manager.process_data_point(data_line)

    def add_data(self, data):
        """ 新增數據到解碼佇列 """
        # print(data)
        if self.running:
            self.data_queue.put(data)

    def start(self):
        """ 使用 QTimer 啟動非阻塞迴圈 """
        logger.info("DecoderWorker 開始運行")
        self.running = True
        self.paused = False  # 確保開始時不處於暫停狀態
        self.timer.start(5)  # 每 100ms 執行一次 process_data

    def pause(self):
        """ 暫停數據處理 """
        logger.info("DecoderWorker 暫停處理")
        self.Dynamic_Tare_Count=[0,False] # 動態tare 計數器歸零[計數器,紀錄(T/F)]
        self.paused = True

        # 暫停自動錄製
        if self.auto_recording_manager.is_recording:
            self.auto_recording_manager._stop_recording()
            self.auto_recording_manager.reset()

        # 暫停手動錄製
        if self.record_state:
            self.set_record_state(False)
        
        '''
        # 手動錄製檔案關閉
        if self.record_state:
            self.close_msra_file()
            # TODO: 更新UI的按鈕成沒有錄影的狀態
        '''

    def resume(self):
        """ 恢復數據處理 """
        logger.info("DecoderWorker 恢復運行")
        self.paused = False

    def process_data(self):
        """ 處理數據（取代 while self.running）"""
        
        if not self.running or self.paused:  # 檢查是否處於暫停狀態
            # logger.debug("DecoderWorker 暫停處理數據...")
            return
        
        if not self.data_queue.empty():
            # logger.debug("DecoderWorker 正在處理數據...")
            # logger.debug(self.filter_data)
            raw = self.data_queue.get()
            try:
                if self.tare_state:
                    decoded = self.socket_tare_data(raw)
                    self.decoded_data.emit(decoded)
                else:
                    decoded = self.socket_decoder(raw)
                    self.decoded_data.emit(decoded)
                
            except Exception as e:
                logger.error(f"解碼失敗: {e}")

    def stop(self):
        """ 安全停止執行緒 """
        logger.info("DecoderWorker 正在停止...")
        self.data_queue.clear()
        self.co2instance.clearAccumulation() #換刀時，CO2累積值應歸零
        self.running = False
        self.paused = False  # 停止時確保不處於暫停狀態
        self.timer.stop()  # 停止 QTimer

    def socket_decoder(self, hex_data: str) -> tuple:
        """
        解碼從socket接收到的十六進制數據
        Args: 十六進制字符串
        Returns: tuple: 包含彎曲力、張力、扭力、溫度、加速度計、WiFi、電池、CO2等數據
        """
      
        Append_index=0  #塞正常資料的index

        MS_BendingX = np.zeros(self.SamplePoint1)
        MS_BendingY = np.zeros(self.SamplePoint1)
        MS_BendingXY = np.zeros(self.SamplePoint1)
        MS_Tension = np.zeros(self.SamplePoint1)
        MS_Torsion = np.zeros(self.SamplePoint1)

        MS_Temperature = np.zeros(self.SamplePoint2)
        MS_ADXL_X = np.zeros(self.SamplePoint2)
        MS_ADXL_Y = np.zeros(self.SamplePoint2)
        MS_ADXL_Z = np.zeros(self.SamplePoint2)

        dynamic_tare_bx=[]
        dynamic_tare_by=[]
        dynamic_tare_ten=[]
        dynamic_tare_tor=[]
        # print(f"Received (hex): {hex_data}")
        # decoded_string = bytes.fromhex(text).decode('utf-8')  # 轉換為字節並解碼
        # logger.error(f"Decoded string: {decoded_string}")
        Collect_data=hex_data
        Data_x=0
        MAC = ""
        RecordTextData=""
        RawData=""
        
        try:
            for i in range(self.sample_N):
                offset=i*32

                if Collect_data[0+offset:2+offset] in ["f1", "f2", "f3", "f4"]:
                    print(f"Collect_data: {Collect_data[0+offset:32+offset]}")
                    continue

                if i%201==0 :  #正常1+200 #第一筆放其他資訊的資料  
                    Data_ADXL_X = int(Collect_data[0+offset:4+offset],16)  
                    Data_ADXL_Y = int(Collect_data[4+offset:8+offset],16)
                    Data_ADXL_Z = int(Collect_data[8+offset:12+offset],16)
                    Data_Temp = int( ( Collect_data[14+offset:16+offset] +  Collect_data[12 +offset:14+offset] ) ,16) /128 #溫度，高  低位元有錯位
                    Wifi_RSSI = int(Collect_data[16+offset:18+offset],16)

                    self.temp_RSSI = Wifi_RSSI
                    txt_gx = round((Data_ADXL_X - self.Tare_GX) ,self.display_decimal_places)  #/10000
                    txt_gy = round((Data_ADXL_Y - self.Tare_GY),self.display_decimal_places)
                    txt_gz = round((Data_ADXL_Z - self.Tare_GZ),self.display_decimal_places)
                    index = i//201

                    MS_ADXL_X[index] = txt_gx
                    MS_ADXL_Y[index] = txt_gy
                    MS_ADXL_Z[index] = txt_gz

                    #TODO: 需要update DB的Tare_GX, Tare_GY, Tare_GZ
                    if self.Tare_GX==0 :
                        self.Tare_GX=np.mean( MS_ADXL_X )
                        self.Tare_GY=np.mean( MS_ADXL_Y )
                        self.Tare_GZ=np.mean( MS_ADXL_Z )
                        # logger.info(f"GX: {self.Tare_GX} GY: {self.Tare_GY} GZ: {self.Tare_GZ}")

                    if 125>Data_Temp:  #DS620溫度感測範圍-55~125
                        MS_Temperature[index] = Data_Temp

                    RecordTextData += "*," + str('{:.3f}'.format(txt_gx)) + ',' + \
                        str('{:.3f}'.format(txt_gy)) + ',' + \
                        str('{:.3f}'.format(txt_gz)) + ',' + \
                        str('{:.2f}'.format(Data_Temp)) + ',' \
                        + str(Wifi_RSSI) + '\n'
                    RawData += "*," + str('{:.3f}'.format(txt_gx)) + ',' + \
                        str('{:.3f}'.format(txt_gy)) + ',' + \
                        str('{:.3f}'.format(txt_gz)) + ',' + \
                        str('{:.2f}'.format(Data_Temp)) + ',' \
                        + str(Wifi_RSSI) + '\n'

                    FLAG_Charge  = 0x01  # 二進位：0001 
                    FLAG_Sleep   = 0x02  # 二進位：0010 
                    flags = int(Collect_data[19+offset:20+offset],16)

                    # 檢查 FLAG 是否被置位(是否被Set)
                    if flags & FLAG_Charge:
                        self.Charging_Flag_temp = True
                        # logger.info("Charging_Flag_temp is on.") # 充電中
                    else:
                        self.Charging_Flag_temp = False
                        # logger.info("Charging_Flag_temp is off.")  
                    
                    if flags & FLAG_Sleep:
                        self.sleep_mode_temp = True
                        # logger.info("sleep_mode_temp is on.") # 睡眠模式已開啟
                    else:
                        self.sleep_mode_temp = False
                        # logger.info("sleep_mode_temp is off.")

                    #--讀MAC--
                    MAC = Collect_data[20+offset:32+offset]
                    self.MAC = MAC
                    continue
 
                Data_x= int(Collect_data[0+offset:4+offset],16) /6553.5  
                Data_y= int(Collect_data[4+offset:8+offset],16) /6553.5  
                Data_ten= int(Collect_data[8+offset:12+offset],16) / 6553.5  
                Data_tor= int(Collect_data[12+offset:16+offset],16) / 6553.5   
                Data_battery=round(int(Collect_data[16+offset:20+offset],16) /6553.5 ,self.display_decimal_places)

                if self.Dynamic_Tare:
                    Data_dynamic_x= int(Collect_data[0+offset:4+offset],16) /65535 *10
                    Data_dynamic_y= int(Collect_data[4+offset:8+offset],16) /65535 *10
                    Data_dynamic_ten= int(Collect_data[8+offset:12+offset],16) /65535 *10 
                    Data_dynamic_tor= int(Collect_data[12+offset:16+offset],16) /65535 *10 
                    dynamic_tare_bx.append( round( Data_dynamic_x,self.display_decimal_places) )
                    dynamic_tare_by.append( round( Data_dynamic_y,self.display_decimal_places) )
                    dynamic_tare_ten.append( round( Data_dynamic_ten,self.display_decimal_places) )
                    dynamic_tare_tor.append( round( Data_dynamic_tor *7.33,self.display_decimal_places) )

                #Tare後的單位轉換資料儲存
                if self.Dynamic_Tare_Count[0] > 0:
                    txt_bx = round((Data_x - self.Dynamic_Tare_BX) * self.parm_N_X,self.display_decimal_places) # Ftx = parm_N_X * Data_x(ΔV)
                    txt_by = round((Data_y - self.Dynamic_Tare_BY)* self.parm_N_Y,self.display_decimal_places) # Ftx = parm_N_Y * Data_y(ΔV)
                    txt_ten = round((Data_ten- self.Dynamic_Tare_BZ)* self.parm_N_Z,self.display_decimal_places) # Ftx = parm_N_Z * Data_z(ΔV)
                    txt_tor = round((Data_tor- self.Dynamic_Tare_BT)* self.parm_N_T,self.display_decimal_places)
                else:
                    txt_bx = round((Data_x - self.Tare_BX) * self.parm_N_X,self.display_decimal_places) # Ftx = parm_N_X * Data_x(ΔV)
                    txt_by = round((Data_y - self.Tare_BY)* self.parm_N_Y,self.display_decimal_places) # Ftx = parm_N_Y * Data_y(ΔV)
                    txt_ten = round((Data_ten- self.Tare_BZ)* self.parm_N_Z,self.display_decimal_places) # Ftx = parm_N_Z * Data_z(ΔV)
                    txt_tor = round((Data_tor- self.Tare_BT)* self.parm_N_T,self.display_decimal_places)

                MS_BendingX[Append_index] = txt_bx   #index -198 是因為i從2開始算，且資料是201後面的200開始算
                MS_BendingY[Append_index] = txt_by
                MS_BendingXY[Append_index] = math.sqrt( (txt_bx*txt_bx) + (txt_by*txt_by) )
                MS_Tension[Append_index] = txt_ten
                MS_Torsion[Append_index] = txt_tor

                self.temp_battery = Data_battery

                #TODO: 存檔加入時間戳記
                RawData += str('{:.{}f}'.format(Data_x, 3)) + ', ' + \
                    str('{:.{}f}'.format(Data_y, 3)) + ', ' + \
                    str('{:.{}f}'.format(Data_ten, 3)) + ', ' + \
                    str('{:.{}f}'.format(Data_tor, 3)) + ', ' + \
                    str('{:.3f}'.format(Data_battery)) + '\n'
                RecordTextData += str('{:.{}f}'.format(txt_bx, 3)) + ', ' + \
                    str('{:.{}f}'.format(txt_by, 3)) + ', ' + \
                    str('{:.{}f}'.format(txt_ten, 3)) + ', ' + \
                    str('{:.{}f}'.format(txt_tor, 3)) + ', ' + \
                    str('{:.3f}'.format(Data_battery)) + '\n'

                Append_index+=1
                #電壓資料
                # print(f"Data_x: {Data_x}")
                # print(f"Data_y: {Data_y}")
                # print(f"Data_ten: {Data_ten}")
                # print(f"Data_tor: {Data_tor}")
                # print(f"Data_battery: {Data_tor}")
                if self.Dynamic_Tare:
                    logger.debug(f"Dynamic_Tare: {self.Dynamic_Tare}")
                    self.Dynamic_Tare = False
                    self.Dynamic_Tare_Count[0]+=1

                    if self.Dynamic_Tare_Count[0] > 0:
                        logger.warning(f"Before: Tare_BX:{self.Tare_BX} Tare_BY:{self.Tare_BY} Tare_Tension:{self.Tare_BZ} Tare_Torsion:{self.Tare_BT}")
                        self.Dynamic_Tare_BX=round(np.mean(dynamic_tare_bx),self.display_decimal_places)
                        self.Dynamic_Tare_BY=round(np.mean(dynamic_tare_by),self.display_decimal_places)
                        self.Dynamic_Tare_Tension=round(np.mean(dynamic_tare_ten),self.display_decimal_places)
                        self.Dynamic_Tare_Torsion=round(np.mean(dynamic_tare_tor),self.display_decimal_places)
                    else:
                        logger.warning(f"Before: Tare_BX:{self.Dynamic_Tare_BX} Tare_BY:{self.Dynamic_Tare_BY} Tare_Tension:{self.Dynamic_Tare_Tension} Tare_Torsion:{self.Dynamic_Tare_Torsion}")
                        self.Dynamic_Tare_BX=round(np.mean(dynamic_tare_bx),self.display_decimal_places)
                        self.Dynamic_Tare_BY=round(np.mean(dynamic_tare_by),self.display_decimal_places)
                        self.Dynamic_Tare_Tension=round(np.mean(dynamic_tare_ten),self.display_decimal_places)
                        self.Dynamic_Tare_Torsion=round(np.mean(dynamic_tare_tor),self.display_decimal_places)
                    
                    logger.warning(f"After: Tare_BX:{self.Dynamic_Tare_BX} Tare_BY:{self.Dynamic_Tare_BY} Tare_Tension:{self.Dynamic_Tare_Tension} Tare_Torsion:{self.Dynamic_Tare_Torsion}")
        except Exception as e:
            logger.error(e)

        try:
            if self.record_state:
                self.msra_file_manager.add_text(self.filename, RecordTextData)
                # 根據 radioButton 狀態決定存檔內容
                if self.save_rawdatafile:
                    self.msra_rawdatafile_manager.add_text(self.rawdata_filename, RawData) 
                
                # logger.debug(f"save_only_recordtext: {self.save_only_recordtext}")
                # logger.debug(f"save_rawdatafile: {self.save_rawdatafile}")

        except FileNotFoundError as e:
            logger.error(f"檔案目錄不存在: {self.directory}, 錯誤: {e}")
        except PermissionError as e:
            logger.error(f"檔案寫入權限不足: {self.filename}, 錯誤: {e}")
        except Exception as e:
            logger.error(f"檔案儲存錯誤: {e}")

        # 套用濾波器
        if self.filter_data["filter_type"] != "Nofilter_radio":
            MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion = self._applyFilter(
                MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion
            )

        #計算CO2
        co2_status=self.CO2_data["init_status"] # 0:不計算, 1: 計算
        if co2_status == 1 :
            co2_g = round(
                self.co2instance.caculate_co2_g(txt_ten,self.SampleRate,self.CO2_data),
                self.display_decimal_places
                )
        else :
            co2_g = 0.0
        
        # Prepare sensor data for auto recording
        sensor_data = (MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, 
                      MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, self.temp_RSSI, 
                      self.Charging_Flag_temp, self.sleep_mode_temp, self.temp_battery, co2_g)
        
        # Process auto recording
        self._process_auto_recording(sensor_data, RecordTextData)
        
        return sensor_data  # 回傳已整理好的完整數據

    def socket_tare_data(self, hex_data):
        Append_index=0  #塞正常資料的index

        MS_BendingX = np.zeros(self.SamplePoint1)
        MS_BendingY = np.zeros(self.SamplePoint1)
        MS_BendingXY = np.zeros(self.SamplePoint1)
        MS_Tension = np.zeros(self.SamplePoint1)
        MS_Torsion = np.zeros(self.SamplePoint1)

        MS_Temperature = np.zeros(self.SamplePoint2)
        MS_ADXL_X = np.zeros(self.SamplePoint2)
        MS_ADXL_Y = np.zeros(self.SamplePoint2)
        MS_ADXL_Z = np.zeros(self.SamplePoint2)
        # print(f"Received (hex): {hex_data}")
        # decoded_string = bytes.fromhex(text).decode('utf-8')  # 轉換為字節並解碼
        # logger.error(f"Decoded string: {decoded_string}")
        Collect_data=hex_data
        Data_x=0
        MAC = ""
        
        try:
            for i in range(self.sample_N):
                offset=i*32

                if Collect_data[0+offset:2+offset] in ["f1", "f2", "f3", "f4"]:
                    print(f"Collect_data: {Collect_data[0+offset:32+offset]}")
                    continue

                if i%201==0 :  #正常1+200 #第一筆放其他資訊的資料  
                    Data_ADXL_X = int(Collect_data[0+offset:4+offset],16)  
                    Data_ADXL_Y = int(Collect_data[4+offset:8+offset],16)
                    Data_ADXL_Z = int(Collect_data[8+offset:12+offset],16)
                    Data_Temp = int( ( Collect_data[14+offset:16+offset] +  Collect_data[12 +offset:14+offset] ) ,16) /128 #溫度，高  低位元有錯位
                    Wifi_RSSI = int(Collect_data[16+offset:18+offset],16)

                    self.temp_RSSI = Wifi_RSSI
                    txt_gx = round(Data_ADXL_X ,self.display_decimal_places)  #/10000
                    txt_gy = round(Data_ADXL_Y,self.display_decimal_places)
                    txt_gz = round(Data_ADXL_Z,self.display_decimal_places)
                    index = i//201

                    MS_ADXL_X[index] = txt_gx
                    MS_ADXL_Y[index] = txt_gy
                    MS_ADXL_Z[index] = txt_gz

                    if 125>Data_Temp:  #DS620溫度感測範圍-55~125
                        MS_Temperature[index] = Data_Temp

                    #--判斷充電-- {"0": "未充電","1": "充電中"}
                    Charging_Flag_temp = int(Collect_data[19+offset:20  +offset],16)  
                    self.Charging_Flag_temp = Charging_Flag_temp

                    #--判斷睡眠模式-- {"b": "沒睡","1": "睡了"}
                    sleep_mode_temp = bin(int(Collect_data[19+offset:20 +offset]))[-2]
                    self.sleep_mode_temp = sleep_mode_temp

                    #--讀MAC--
                    MAC = Collect_data[20+offset:32+offset]
                    self.MAC = MAC
                    continue

                Data_x= int(Collect_data[0+offset:4+offset],16) /6553.5  
                Data_y= int(Collect_data[4+offset:8+offset],16) /6553.5  
                Data_ten= int(Collect_data[8+offset:12+offset],16) / 6553.5  
                Data_tor= int(Collect_data[12+offset:16+offset],16) / 6553.5   
                Data_battery=round(int(Collect_data[16+offset:20+offset],16) /6553.5 ,self.display_decimal_places)

                MS_BendingX[Append_index] = Data_x   
                MS_BendingY[Append_index] = Data_y
                MS_BendingXY[Append_index] = math.sqrt( (Data_x*Data_x) + (Data_y*Data_y) )
                MS_Tension[Append_index] = Data_ten
                MS_Torsion[Append_index] = Data_tor
                self.temp_battery = Data_battery
                Append_index+=1
                #電壓資料
                # print(f"Data_x: {Data_x}")
                # print(f"Data_y: {Data_y}")
                # print(f"Data_ten: {Data_ten}")
                # print(f"Data_tor: {Data_tor}")
                # print(f"Data_battery: {Data_tor}")
        except Exception as e:
            logger.error(f"發生錯誤：{e}")

        return (MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, 
            MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z , self.temp_RSSI ,self.Charging_Flag_temp ,self.sleep_mode_temp, self.temp_battery)  # 回傳已整理好的完整數據
    
    def __del__(self): 
        logger.info(f'DecoderWorker {self.current_holder_data["toolip"]} 已被銷毀')

    def _applyFilter(self, bending_x, bending_y, bending_xy, tension, torsion) -> tuple:
        """
        應用濾波器處理數據
        
        Args:
            bending_x: 輸入的X軸彎曲數據
            bending_y: 輸入的Y軸彎曲數據  
            bending_xy: 輸入的XY軸彎曲合成數據
            tension: 輸入的張力數據
            torsion: 輸入的扭力數據
            
        Returns:
            tuple: 濾波後的五個數據陣列 (bending_x, bending_y, bending_xy, tension, torsion)
        """
        # 沒用濾波器則直接回傳
        if self.filter_data["filter_type"] == "Nofilter_radio":
            return bending_x, bending_y, bending_xy, tension, torsion
            
        # 取得濾波器參數
        k = self.filter_data["filter_values"]
        
        # 根據濾波器類型進行處理
        if self.filter_data["filter_type"] == "Meanfilter_radio":
            # 均值濾波器
            bending_x = self.Mean_filter(bending_x, k)
            bending_y = self.Mean_filter(bending_y, k)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Mean_filter(tension, k)
            torsion = self.Mean_filter(torsion, k)

        elif self.filter_data["filter_type"] == "Medianfilter_radio":
            # 中值濾波器
            bending_x = self.Median_filter(bending_x, k)
            bending_y = self.Median_filter(bending_y, k)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Median_filter(tension, k)
            torsion = self.Median_filter(torsion, k)

        elif self.filter_data["filter_type"] == "Gaussianfilter_radio":
            # 高斯濾波器
            bending_x = self.Gaussian_filter(bending_x, k, 1)
            bending_y = self.Gaussian_filter(bending_y, k, 1)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Gaussian_filter(tension, k, 1)
            torsion = self.Gaussian_filter(torsion, k, 1)
            
        elif self.filter_data["filter_type"] == "MSfilter_radio":
            # Machsync濾波器
            bending_x = self.MS_Filter(bending_x, k, 0.003, 10, self.SampleRate)
            bending_y = self.MS_Filter(bending_y, k, 0.003, 10, self.SampleRate)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.MS_Filter(tension, k, 0.003, 10, self.SampleRate)
            torsion = self.MS_Filter(torsion, k, 0.003, 10, self.SampleRate)

        elif self.filter_data["filter_type"] == "Lowfilter_radio":
            # 低通濾波器
            cutoff_low = int(self.filter_data["Lowfilter_edit"])
            bending_x = self.Butterworth_filter(bending_x, cutoff_low, self.SampleRate, "low", order=2)
            bending_y = self.Butterworth_filter(bending_y, cutoff_low, self.SampleRate, "low", order=2)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Butterworth_filter(tension, cutoff_low, self.SampleRate, "low", order=2)
            torsion = self.Butterworth_filter(torsion, cutoff_low, self.SampleRate, "low", order=2)
                
        elif self.filter_data["filter_type"] == "Highfilter_radio":
            # 高通濾波器
            cutoff_high = int(self.filter_data["Highfilter_edit"])
            bending_x = self.Butterworth_filter(bending_x, cutoff_high, self.SampleRate, "high")
            bending_y = self.Butterworth_filter(bending_y, cutoff_high, self.SampleRate, "high")
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Butterworth_filter(tension, cutoff_high, self.SampleRate, "high")
            torsion = self.Butterworth_filter(torsion, cutoff_high, self.SampleRate, "high")
            
        elif self.filter_data["filter_type"] == "SGfilter_radio":
            # Savitzky-Golay濾波器
            WL = int(self.filter_data["SGfilter_edit"])
            bending_x = self.Savitzky_Golay_filter(bending_x, WL, 3) 
            bending_y = self.Savitzky_Golay_filter(bending_y, WL, 3)
            bending_xy = np.sqrt(bending_x**2 + bending_y**2)
            tension = self.Savitzky_Golay_filter(tension, WL, 3)
            torsion = self.Savitzky_Golay_filter(torsion, WL, 3)
            
        elif self.filter_data["filter_type"] == "MAfilter_radio":
            # 簡單移動平均
            MV = int(self.filter_data["MAfilter_edit"])
            Avg_BendingX = pd.Series(bending_x)
            Avg_BendingY = pd.Series(bending_y)
            Avg_BendingXY = pd.Series(np.sqrt(bending_x**2 + bending_y**2))
            Avg_Tension = pd.Series(tension)
            Avg_Torsion = pd.Series(torsion)
            bending_x = Avg_BendingX.rolling(window=MV, min_periods=1).mean()
            bending_y = Avg_BendingY.rolling(window=MV, min_periods=1).mean()
            bending_xy = Avg_BendingXY.rolling(window=MV, min_periods=1).mean()
            tension = Avg_Tension.rolling(window=MV, min_periods=1).mean()
            torsion = Avg_Torsion.rolling(window=MV, min_periods=1).mean()
            
        else:
            # 未定義的濾波器類型
            logger.debug(f"undefined filter type: {self.filter_data['filter_type']}")

        return bending_x, bending_y, bending_xy, tension, torsion

    # TAG: 濾波功能
    def Moving_average(self, new_value, moving_average):
        """ 移動平均濾波 
        :param new_value: 新的數據值
        :param moving_average: 移動平均窗口大小
        :return: 濾波後的數據值
        """

        Avg_Bending = pd.Series(new_value)
        Avg_value = Avg_Bending.rolling(window=moving_average, min_periods=1).mean()

        return Avg_value

    #---濾波事件---
    def Mean_filter (self, x, k):
        """
        均值濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數

        Apply a length-k mean filter to a 1D array x.
        Boundaries are extended by repeating endpoints.
        """
        assert k % 2 == 1, "Median filter length must be odd."
        assert x.ndim == 1, "Input must be one-dimensional."

        k2 = (k - 1) // 2
        y = np.zeros ((len (x), k), dtype=x.dtype)
        y[:,k2] = x

        for i in range (k2):
            j = k2 - i
            y[j:,i] = x[:-j]
            y[:j,i] = x[0]
            y[:-j,-(i+1)] = x[j:]
            y[-j:,-(i+1)] = x[-1]
        return np.mean (y, axis=1) #.tolist() # AIE +++ Serena [2023/10/23 list -> array]

    def Median_filter (self, x, k):
        """
        中值濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數

        Apply a length-k median filter to a 1D array x.
        Boundaries are extended by repeating endpoints.
        """
        assert k % 2 == 1, "Median filter length must be odd."
        assert x.ndim == 1, "Input must be one-dimensional."

        k2 = (k - 1) // 2
        y = np.zeros ((len (x), k), dtype=x.dtype)
        y[:,k2] = x
        for i in range (k2):
            j = k2 - i
            y[j:,i] = x[:-j]
            y[:j,i] = x[0]
            y[:-j,-(i+1)] = x[j:]
            y[-j:,-(i+1)] = x[-1]
        return np.median (y, axis=1) 

    def Gaussian_filter(self, x, size, sigma):
        """
        高斯濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param K:  filter_Slider = 舊版 K值變數
        :param sigma: 1  預設 1
        """

        if size % 2 == 0:
            size += 1  # 將 size 設置為奇數

        filter_range = np.linspace(-int(size / 2), int(size / 2), size)
        gaussian_filter = [1 / (sigma * np.sqrt(2*np.pi)) * np.exp(-i**2 / (2*sigma**2)) for i in   filter_range]  #做高斯filter
        x = np.concatenate((np.repeat(x[0], int(size / 2)), x, np.repeat(x[-1], int(size / 2))))

        return np.convolve(x, gaussian_filter, mode='valid') #.tolist()  # AIE +++ Serena [2023/10/ 23 list -> array]

    def MS_Filter(self, X,Filter_Size,Diff_Value,Result_Value,Sample_Rate):
        """
        Machsync濾波
        :param x: MS_BendingX Y Z T 之一的數據
        :param Filter_Size: filter_Slider = 舊版 K值變數
        :param Diff_Value: 0.003
        :param Result_Value: 10
        :param Sample_Rate: SampleRate

        """

        Result_List = self.Median_filter(np.array(X),Filter_Size)
        Result_List = self.Mean_filter(np.array(Result_List),Filter_Size)
        MS_Sample_Rate=Sample_Rate

        diff_X=[]
        diff_time=[]
        index=0

        for i in range(0,len(Result_List)-1):
            diff_X.append( abs(Result_List[i]-Result_List[i+1] ) )

            diff_time.append(index)
            index+=1
        # mean_diff_X = round(np.mean(diff_X),3)

        tolerance_count=0
        test_start=False
        threshold_value=max(diff_X)/2
        Result_Value= round(max(X, key=abs)/Result_Value,self.display_decimal_places)

        for i in range(0,len(diff_X)):
            if (diff_X[i] > threshold_value and diff_X[i]>Diff_Value) or Result_List[i] >=Result_Value :  #0.01  or X[i]>=0.1   or  (diff_X[i] > mean_diff_X and     mean_diff_X!=0)

                tolerance_count=MS_Sample_Rate*0.05  #保留0.05s資料
                Result_List[i] = X[i]  #保留原資料

                if test_start==False:  #第一次超越起伏，保留前0.1s資料
                    temp_i = int(i - tolerance_count) if i - tolerance_count>=0 else 0  #以防超越第 0筆資料
                    Result_List[temp_i:i] = X[temp_i:i] 

                test_start=True
            else:
                if tolerance_count>0:
                    tolerance_count-=1
                    Result_List[i] = X[i]  #保留原資料
                else:
                    test_start=False

        return Result_List

    def Butterworth_filter(self, x, cutoff_freq, fs, btype , order=4):
        
        """
        應用Butterworth濾波器
        # 低通濾波 low / 高通濾波 high / 帶通濾波 band
        :param x: MS_BendingX Y Z T 之一的數據
        :param cutoff_freq: 截止頻率 (Hz)
        :param fs: 採樣頻率 (Hz)
        :param btype: low / 濾波器類型 ('low', 'high', 'band')
        :param order: 濾波器階數 SampleRate
        
        """
        from scipy import signal
        nyquist = fs #/ 2
        normalized_cutoff_freq = cutoff_freq / nyquist
        b, a = signal.butter(order, normalized_cutoff_freq, btype)
        return signal.filtfilt(b, a, x)

    def Savitzky_Golay_filter(self, x, window_length, polyorder):
        """
        應用Savitzky-Golay濾波器

        :param x: MS_BendingX Y Z T 之一的數據
        :param window_length: WL / 窗口長度（必須是奇數） 
        :param polyorder: 3 / 多項式階數
        """
        if window_length % 2 == 0:
            window_length += 1
        return savgol_filter(x, window_length, polyorder)
    
    #---計算CO2 g---
    #TAG:計算CO2 g 造成卡頓時，備案
    # def caculate_co2_g(self,txt_ten, SampleRate): 
    #     MS_CO2_Pc = self.CO2_data["MS_CO2_Pc"]
    #     MS_CO2_Pb = self.CO2_data["MS_CO2_Pb"]
    #     MS_CO2_EF = self.CO2_data["MS_CO2_EF"]

    #     if abs(txt_ten)>3:  #Tension若大於3則開始觸發，0.05是手用力測試
    #         self.CO2_Pc_flag=True
    #         CO2_Pc_interval_trigger = 0.02 * SampleRate  #0.02秒
    #         self.CO2_Pc_interval_count = CO2_Pc_interval_trigger
    #     else:
    #         self.CO2_Pc_interval_count-=1
    #         if 0 >= self.CO2_Pc_interval_count:  
    #             self.CO2_Pc_flag=False  #在0.02秒內沒有Tension大於3，判定沒在切削了
            
    #     if self.CO2_Pc_flag:  #Pc+Pb
    #         self.CO2_accumulation += (MS_CO2_Pc + MS_CO2_Pb) * MS_CO2_EF /3.6 / SampleRate  #*1000 /3600 => /3.6
    #     else:  #Pb
    #         self.CO2_accumulation += MS_CO2_Pb * MS_CO2_EF /3.6/ SampleRate

    #     return np.round( self.CO2_accumulation ,3)