# controller.py
"""
Controller 類別 - 主要控制器

新增功能：
1. Socket 自動重連機制
   - 自動檢測連線中斷並重連
   - 可配置重連參數（間隔、次數、超時）
   - 心跳檢測機制

2. 連線狀態管理
   - 實時監控連線狀態
   - 連線狀態變化通知
   - 優雅的錯誤處理

使用方法：
- configure_socket_reconnect(): 配置自動重連參數
- force_socket_reconnect(): 強制重連
- get_socket_status(): 獲取連線狀態
- disable_auto_reconnect(): 禁用自動重連
"""

import sys, os
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import config
import time
import math
import subprocess # 系統檢查報告用
import openpyxl
from functools import partial
from utils.cirular_queue import CircularQueue
import numpy as np
from  utils.socket_work import SocketWorker
from  app.models.decoder_Worker import DecoderWorker
from app.models.network_device_manager import search_all_network_devices
from . import logger  # 從同一個包導入 logger

class Controller:
    
    def __init__(self, model, view):
        logger.debug("Initializing Controller")
        self.model = model
        self.view = view

        self.data_queue = CircularQueue(maxsize=10)
        self.setting_data = None # self.setting_data['msra_file_path']
        self.current_data = None

        self.msra_file_name = "ToolName_" + time.strftime("%Y%m%d_%H%M%S",time.localtime()) + ".msra" # 記錄檔名 預設
        self.msra_file_path = None # 記錄檔路徑
        

        #  初始化執行緒（只建立一次）
        self.socket_thread = QThread()
        self.decoder_thread = QThread()

        self.saved_angle = None  # 用來儲存 3D 平面角度
        self.is_long_press = False
        self.previous_plot_type = None  # 用來儲存之前的 plot_type

        self.threads_running = False  #  新增變數，避免重複啟動執行緒
        self.count = 0 #  新增變數，用來計數
        self.record_decimal_places = 6   # 預設紀錄用小數位數
        self.display_decimal_places = 3  # 預設顯示小數位數 

        # TAG: Tare holder 資料先暫時存檔 可能會在移動 
        self.tare_BendingX = None
        self.tare_BendingY = None
        self.tare_Tension = None
        self.tare_Torsion = None
        self.tare_ADXL_X = None
        self.tare_ADXL_Y = None
        self.tare_ADXL_Z = None

        # 系統檢查用資料
        self.RSSI_max = None
        self.RSSI_min = None
        self.temperature_max = None
        self.battery_min = None
        self.bx_max = None
        self.bx_min = None
        self.by_max = None
        self.by_min = None
        self.tension_max = None
        self.tension_min = None
        self.torsion_max = None
        self.torsion_min = None
        self.ax_max = None
        self.ax_min = None
        self.ay_max = None
        self.ay_min = None
        self.az_max = None
        self.az_min = None

        self.test_count = 0 #  新增變數，用來計數
        self.CO2_id = None

        self.button_binding() # 綁定按鈕
        self.signal_binding() # 綁定信號
        self.base_setting()  # 載入設定檔資料

    def base_setting(self):
        # 載入設定檔資料
        logger.info("載入設定檔資料")
        self.model.connect_db()
        machradar_set_data = self.model.get_machradar_set()
        self.model.close_db()
        self.setting_data = dict(machradar_set_data[0])
        self.setting_data["save_rawdatafile"] = False
        self.msra_file_path = self.setting_data['msra_file_path']
        # logger.error(f"xxx:{self.setting_data['msra_file_path']}")


    def button_binding(self):
         # 連接視圖的按鈕到控制器的槽
        self.view.Btn_Link.clicked.connect(self.widget_redraw)
        
        self.view.Btn_Toolname.clicked.connect(self.show_Toollist_window) #  顯示Toollist_window
        self.view.Btn_Toolname.clicked.connect(lambda: QTimer.singleShot(10, self.search_network_devices)) #  延遲搜尋網路設備
        self.view.Btn_Setting.clicked.connect(self.show_Setting_window) #  顯示Setting_window
        self.view.Btn_Filter.clicked.connect(self.show_Filter_window) #  顯示Filter_window
        self.view.Btn_View.clicked.connect(self.show_View_window) #  顯示View_window
        self.view.Btn_Event.clicked.connect(self.show_Event_window) #  顯示Event_window
        self.view.Btn_Mode.clicked.connect(self.show_ModeSwitch_window) #  顯示ModeSwitch_window
        self.view.Btn_RecordScript.clicked.connect(self.show_RecordScript_window) #  顯示RecordScript_window
        self.view.Btn_Help.clicked.connect(self.show_Help_window) #  顯示Help_window
        self.view.Btn_CO2.clicked.connect(self.show_CO2e_window) #  顯示CO2_window
        self.view.Btn1_Tare.clicked.connect(self.Tare_holder_window) #  綁定 Tare 按鈕

        self.view.Btn4_ResetAngle.clicked.connect(self.reset_angle) #  綁定 ResetAngle 按鈕
        self.view.Btn3_PlotMode.clicked.connect(self.plot_mode) #  綁定 PlotMode 按鈕
        self.view.Btn6_angle1.clicked.connect(self.handle_angle_button) #  綁定 angle1 按鈕
        self.view.Btn6_angle1.pressed.connect(self.start_cancel_angle)
        self.view.Btn6_angle1.released.connect(self.stop_cancel_angle)
        self.view.Btn2_BGMode.clicked.connect(self.BG_mode) #  綁定 BGMode 按鈕
        self.view.Btn7_sleep_mode.clicked.connect(self.sleepmode_UP) #  綁定 sleep_mode 按鈕
        self.view.Btn8_target.clicked.connect(self.show_target) #  綁定 target 按鈕

        self.view.Btn_Record.clicked.connect(self.Btn_record_event) #  綁定 Btn_Record 按鈕

        self.view.CF_btn.clicked.connect(lambda: self.bending_visibility("XY")) #  綁定 CF_btn 按鈕
        self.view.Fx_btn.clicked.connect(lambda: self.bending_visibility("X")) #  綁定 Fx_btn 按鈕
        self.view.Fy_btn.clicked.connect(lambda: self.bending_visibility("Y")) #  綁定 Fy_btn 按鈕


    def signal_binding(self):
        # 連接 View 的信號到 Controller 的處理函數
        self.view.view_current_data.connect(self.get_current_data)
        self.view.update_co2_data.connect(self.update_co2_data)
        self.view.sigSaveFileData.connect(self.update_msra_file_data)
        self.view.sigUpdateToolList.connect(self.show_Toollist_window)
        self.view.sigDoSystemCheck.connect(self.system_check) #  綁定 SystemCheck 訊號
        self.view.sigDoTare.connect(self.Tare_holder_window) #  綁定 Tare 訊號 (目前 Toolinfo_window 會用到)
        self.view.sigSaveToolInfo.connect(self.save_toolinfo) #  綁定 SaveToolInfo 訊號 (目前 Toolinfo_window 會用到)
        self.view.sigSaveAutoRecordingSettings.connect(self.save_auto_recording_settings) #  綁定 SaveAutoRecordingSettings 訊號 
        self.view.sigViewRWFileData.connect(self.save_read_msrb_file) #  綁定 sigViewRWFileData 訊號 

    def Btn_record_event(self):
        """處理錄製按鈕事件"""
        record_status = self.view.Btn_Link.isChecked()
        logger.warning(f"{record_status} ; Btn_Link")

        # 如果未連線，阻止狀態改變並顯示警告
        if not record_status:
            # 保存當前按鈕狀態
            current_state = self.view.Btn_Record.isChecked()
            # TAG: 要再深入研究  blockSignals 用法
            # 暫時阻止信號以避免遞迴 
            self.view.Btn_Record.blockSignals(True)
            # 將按鈕狀態設回原狀
            self.view.Btn_Record.setChecked(False)
            # 恢復信號
            self.view.Btn_Record.blockSignals(False)

            # TODO: 顯示警告視窗 要改成UI顯示
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("警告")
            msg.setText("請先確認連線狀態！")
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec_()

            return
    
        # 如果已連線，則執行原本的邏輯
        if self.view.Btn_Record.isChecked():
            logger.debug(f"Btn_Record 狀態：On {self.view.Btn_Record.isChecked()}")
            data = self.current_data
            directory = self.msra_file_path  # 指定目錄名稱
            # 更新錄製檔預設名稱：刀具名稱
            self.msra_file_name = self.current_data['toolname'] + "_" + time.strftime("%Y%m%d_%H%M%S", time.localtime()) + ".msra"
            logger.debug(f"msra_file_name: {self.msra_file_name}")
            self.decoder_worker.update_msra_file_data(self.msra_file_path, self.msra_file_name)
            self.decoder_worker.update_record_state.emit(True)

        else:
            logger.debug(f"Btn_Record 狀態：Off {self.view.Btn_Record.isChecked()}")
            self.decoder_worker.update_record_state.emit(False)

    def widget_redraw(self):
        
        # 判斷執行緒是否運行 threads_running = False socket_thread = True decoder_thread = True 代表斷線
        if not self.threads_running and self.socket_thread.isRunning() and self.decoder_thread.isRunning():
            self.get_current_data(self.current_data)
            """處理連線按鈕事件"""
        if self.view.Btn_Link.isChecked():
            logger.debug("Btn_Link 狀態：On")
            # # 如果正在進行 Tare 操作，先停止 Tare
            # if hasattr(self, 'decoder_worker') and self.decoder_worker.tare_state:
            #     self.decoder_worker.tare_state = False
            #     self.stop_threads()
            
            # 啟動或恢復執行緒
            if self.threads_running:
                self.resume_threads()
            else:
                self.start_threads()
            
            self.view.start_timer()
            self.view.view_current_data.disconnect(self.get_current_data)
           
        else:
            logger.debug("Btn_Link 狀態：Off")
            # 如果正在進行 Tare 操作，不停止執行緒
            # if not (hasattr(self, 'decoder_worker') and self.decoder_worker.tare_state):
            #     self.stop_threads()
            self.stop_threads()
            self.view.stop_timer()
            self.view.view_current_data.connect(self.get_current_data)
            
            # 停止(手動)錄製，UI 按鈕狀態改為 Off
            self.view.Btn_Record.setChecked(False)
    
    def show_Toollist_window(self):
        """處理Toolname按鈕事件"""
        # 檢查是否正在更新
        if hasattr(self, '_is_updating_tool_list') and self._is_updating_tool_list:
            return

        try:
            self._is_updating_tool_list = True
            """DB 取得資料""" 
            self.model.connect_db()
            toolSettingTable = self.model.get_tool_data()
            self.model.close_db()

            logger.debug("Btn_Toolname 狀態：On")
            self.view.show_Toollist_window(toolSettingTable)
        except Exception as e:
            logger.error(f"更新工具列表時發生錯誤: {e}")
        finally:
            self._is_updating_tool_list = False

    def show_Setting_window(self):
        """處理Setting按鈕事件"""

        if self.view.Btn_Setting.isChecked():
            self.model.connect_db()
            machradar_set_data = self.model.get_machradar_set()
            # self.model.get_tool_data()  # 這行如果沒用到可以省略
            self.model.close_db()
           
            logger.debug(f"current_data: {self.current_data}")

            # 直接用 try-except 取值
            try:
                tool_name = self.current_data['toolname']
                if not tool_name:
                    raise ValueError("toolname 為空")
            except Exception as e:
                logger.error(f"無法取得 toolname，使用預設 ToolName。錯誤：{e}")
                tool_name = "ToolName"

            logger.debug(f"tool_name: {tool_name}")
            self.msra_file_name = tool_name + "_" + time.strftime("%Y%m%d_%H%M%S", time.localtime()) + ".msra"

            save_rawdatafile = self.setting_data.get("save_rawdatafile", False)
            self.setting_data = dict(machradar_set_data[0])
            self.record_decimal_places = self.setting_data['record_decimal_places']
            self.display_decimal_places = self.setting_data['display_decimal_places']
            if hasattr(self, 'decoder_worker'):
                self.decoder_worker.display_decimal_places = self.display_decimal_places
                self.decoder_worker.record_decimal_places = self.record_decimal_places
            self.view.display_decimal_places = self.display_decimal_places
            logger.debug(f"Btn_Setting 狀態：On {self.msra_file_name}")

            # 將 save_rawdatafile 狀態傳給 view
            if hasattr(self, 'decoder_worker'):
                self.setting_data["save_rawdatafile"] = self.decoder_worker.save_rawdatafile
            else: 
                self.setting_data["save_rawdatafile"] = save_rawdatafile

            self.view.show_Setting_window(self.setting_data, self.msra_file_name)

    def show_Filter_window(self):
        """處理Filter按鈕事件"""
        if self.view.Btn_Filter.isChecked():
            logger.debug("Btn_Filter 狀態：On")
            self.view.show_Filter_window()

    def show_View_window(self):
        """處理View按鈕事件"""
        if self.view.Btn_View.isChecked():
            logger.debug("Btn_View 狀態：On")
            self.view.show_View_window()

    def show_Event_window(self):
        """處理Event按鈕事件"""
        if self.view.Btn_Event.isChecked():
            logger.debug("Btn_Event 狀態：On")
            self.view.show_Event_window()

    def show_ModeSwitch_window(self):
        """處理ModeSwitch按鈕事件"""
        if self.view.Btn_Mode.isChecked():
            logger.debug("Btn_ModeSwitch 狀態：On")
            self.view.show_ModeSwitch_window()
    
    def show_RecordScript_window(self):
        """處理RecordScript按鈕事件"""
        if self.view.Btn_RecordScript.isChecked():
            logger.debug("Btn_RecordScript 狀態：On")
            self.view.show_RecordScript_window()

    def show_Help_window(self):
        """處理Help按鈕事件"""
        if self.view.Btn_Help.isChecked():
            logger.debug("Btn_Help 狀態：On")
            self.view.show_Help_window()

    def show_CO2e_window(self):
        """處理CO2按鈕事件"""
        if self.view.Btn_CO2.isChecked():
            """DB 取得資料""" 
        if self.current_data: # 已連線刀把
            if self.CO2_id:  # 刀把有相對應CO2_id
                self.model.connect_db()
                co2Table = self.model.get_co2_data(self.CO2_id) #  取得與刀把相對應的資料
                self.model.close_db()
                logger.debug("Btn_CO2 狀態：On")
                self.view.show_CO2e_window(co2Table)
            else: # 刀把若無相對應CO2_id
                logger.error("CO2_id is None")
        else : # 未連線刀把
            logger.error("No current_data")
            self.view.show_Remind_window("請先連線刀把")

    # TAG: Tare_holder_window
    def Tare_holder_window(self):
        """ Tare 功能"""
        logger.info("Tare 功能")
        tare_status = self.view.Btn1_Tare.isChecked()
        # logger.error(f"{tare_status} ; Tare_holder_window")

        # 檢查連線狀態
        if not self.current_data:
            logger.warning("未連線刀把")
            self.view.show_Remind_window("請先連線刀把")
            return
        
        self.view.eventTareBtn()

        logger.error(f"socket_run {self.socket_worker.running}")

        if not self.threads_running and not self.socket_worker.running:
            if not self.decoder_worker.tare_state:
                self.decoder_worker.tare_state = True
            self.switch_decoder_slot(self.update_tare_holder)
            # logger.error("未連線啟動執行緒")
            self.threads_running = True
            self.socket_thread.start()
            self.decoder_thread.start()
            self.decoder_worker.start()
            # 設置 10 秒後停止
            QTimer.singleShot(10000, self.stop_threads)
        
        elif not self.threads_running and self.socket_worker.running:
            if not self.decoder_worker.tare_state:
                self.decoder_worker.tare_state = True
            self.switch_decoder_slot(self.update_tare_holder)
            # logger.error("連線中啟動執行緒")
            self.threads_running = True
            self.socket_worker.resume()  #  直接恢復 
            self.decoder_worker.resume()  #  直接恢復 `DecoderWorker`，而非重新啟動
            # 設置 10 秒後停止
            QTimer.singleShot(10000, self.stop_threads)
            

        else:
            # 動態 Tare
            logger.debug("動態 Tare")
            self.decoder_worker.Dynamic_Tare = True


    def system_check(self, tool_data):
        """處理 SystemCheck 按鈕事件"""
        # -- 待移到model --
        logger.debug("SystemCheck 按鈕事件")
        
        # 檢查連線狀態
        if not self.current_data or self.current_data["toolip"] != tool_data["toolip"]:
            logger.warning("未連線刀把")
            self.view.show_Remind_window("請先連線刀把")
            return

        # 顯示系統檢查視窗
        self.view.show_System_check_window(tool_data)

        # 收數據
        # -- 待簡化 --
        self.system_check_timer = QTimer()
        self.system_check_timer.timeout.connect(partial(self.generate_system_check_report, tool_data))
        self.system_check_timer.timeout.connect(self.stop_threads)
        self.system_check_timer.setSingleShot(True)
        
        if not self.threads_running and not self.socket_worker.running:
            if not self.decoder_worker.tare_state:
                self.decoder_worker.tare_state = True
            self.switch_decoder_slot(self.update_tare_holder)
            # logger.error("未連線啟動執行緒")
            self.threads_running = True
            self.socket_thread.start()
            self.decoder_thread.start()
            self.decoder_worker.start()
            # 設置 10 秒後停止
            self.system_check_timer.start(10000)
        
        elif not self.threads_running and self.socket_worker.running:
            if not self.decoder_worker.tare_state:
                self.decoder_worker.tare_state = True
            self.switch_decoder_slot(self.update_tare_holder)
            # logger.error("連線中啟動執行緒")
            self.threads_running = True
            self.socket_worker.resume()  #  直接恢復 
            self.decoder_worker.resume()  #  直接恢復 `DecoderWorker`，而非重新啟動
            # 設置 10 秒後停止
            self.system_check_timer.start(10000)

        else:
            # 動態 Tare
            logger.debug("動態 Tare")
            self.decoder_worker.Dynamic_Tare = True
            self.system_check_timer.start(1000)
        

    def generate_system_check_report(self, tool_data):
        """生成系統檢查報告"""
        
        # 寫到excel
        report_template_path = "./system_check/MSST system check template.xlsx"
        report_path = r'.\system_check\MSST system check_' + time.strftime("%Y%m%d%H%M%S",time.localtime()) + '.xlsx'
        report_workbook = openpyxl.load_workbook(report_template_path)
        report_worksheet = report_workbook['MSST_report1']
        
        report_worksheet['C4'] = tool_data['toolname'] # "MSST_BT40_L135_ER20_A001"
        report_worksheet['H4'] = tool_data['toolip'] #"*************"
        report_worksheet['C7'] = tool_data['sample_rate'] #10000 #Frequency
        report_worksheet['C12'] = str(self.RSSI_max) + " ~ " + str(self.RSSI_min) + " dBm" #RSSI range
        report_worksheet['C13'] = str( round(self.temperature_max,2) ) + "℃" #Temperature
        report_worksheet['C14'] = str( round(self.battery_min,2) ) + "V" #Battery V
        
        report_worksheet['C15'] = "Bending X: " + str(round(self.tare_BendingX,3)) + " ± " + str( round( max( (self.bx_max-self.tare_BendingX) , (self.tare_BendingX - self.bx_min) ) ,3 )  )
        report_worksheet['C16'] = "Bending Y: " + str(round(self.tare_BendingY,3)) + " ± " + str( round( max( (self.by_max-self.tare_BendingY) , (self.tare_BendingY - self.by_min) ) ,3 )  )
        report_worksheet['C17'] = "Tension: " + str(round(self.tare_Tension,3)) + " ± " + str( round( max( (self.tension_max-self.tare_Tension) , (self.tare_Tension - self.tension_min) ) ,3 )  )
        report_worksheet['C18'] = "Torsion: " + str(round(self.tare_Torsion,3)) + " ± " + str( round( max( (self.torsion_max-self.tare_Torsion) , (self.tare_Torsion - self.torsion_min) ) ,3 )  )
        report_worksheet['C19'] = "ADXL X: " + str(round(self.tare_ADXL_X,3)) + " ± " + str( round( max( (self.ax_max-self.tare_ADXL_X) , (self.tare_ADXL_X - self.ax_min) ) ,3 )  )
        report_worksheet['C20'] = "ADXL Y: " + str(round(self.tare_ADXL_Y,3)) + " ± " + str( round( max( (self.ay_max-self.tare_ADXL_Y) , (self.tare_ADXL_Y - self.ay_min) ) ,3 )  )
        report_worksheet['C21'] = "ADXL Z: " + str(round(self.tare_ADXL_Z,3)) + " ± " + str( round( max( (self.az_max-self.tare_ADXL_Z) , (self.tare_ADXL_Z - self.az_min) ) ,3 )  )
        
        report_worksheet['G23'] = time.strftime("%Y/%m/%d %H:%M:%S",time.localtime())

        report_workbook.save(report_path)

        # 顯示system_check檔案夾
        subprocess.call('explorer /select,' + report_path)  #開啟檔案路徑並指定

        logger.debug("generate system check report end")


    def save_toolinfo(self, tool_data):
        """儲存用戶更新的刀把資料"""
        logger.debug("save toolinfo start")
        logger.debug(f"tool_data: {tool_data}")

        # 更新 DB 資料
        self.model.connect_db()
        self.model.update_tool_data(tool_data)
        self.model.close_db()


    def save_auto_recording_settings(self, tool_data):
        """儲存自動錄製設定"""
        logger.debug("save auto recording settings start")
        logger.debug(f"tool_data: {tool_data}")

        # 更新 DB 資料
        self.save_toolinfo(tool_data)
        
        # 產生刀把資訊文字(msra檔第一行)
        # 暫時性，未來可以直接加入tool_data內
        tool_info_text = f"!,{tool_data['sample_rate']},{round(tool_data['tare_xv'],self.record_decimal_places)},{round(tool_data['tare_yv'],self.record_decimal_places)},{round(tool_data['tare_zv'],self.record_decimal_places)},{round(tool_data['tare_tv'],self.record_decimal_places)},{tool_data['Linear_x']},{tool_data['Linear_y']},{tool_data['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{tool_data['Lc']},{tool_data['Hl']},{tool_data['Kl']},{tool_data['Bx_COMP']},{tool_data['By_COMP']},{tool_data['Bz_COMP']},{tool_data['Bt_COMP']}\n"

        # 如果當前連接的是這個工具，更新自動錄製設定
        if hasattr(self, 'current_data') and self.current_data and isinstance(self.current_data, dict) and self.current_data.get('toolmac') == tool_data.get('toolmac'):
            auto_recording_settings = {
                'auto_record_enabled': tool_data.get('auto_record_enabled', False),
                'max_record_count_index': 0,  # 將根據 auto_max_record_count 計算
                'auto_record_seconds': tool_data.get('auto_record_seconds', 10),
                'auto_pre_record_seconds': tool_data.get('auto_pre_record_seconds', 0),
                'auto_cf_enabled': tool_data.get('auto_cf_enabled', False),
                'auto_cf_threshold': tool_data.get('auto_cf_threshold', 0),
                'auto_fz_enabled': tool_data.get('auto_fz_enabled', False),
                'auto_fz_threshold': tool_data.get('auto_fz_threshold', 0),
                'auto_t_enabled': tool_data.get('auto_t_enabled', False),
                'auto_t_threshold': tool_data.get('auto_t_threshold', 0),
                'tool_info_text': tool_info_text,
            }
            
            # 計算 max_record_count_index 基於 auto_max_record_count
            auto_max_record_count = tool_data.get('auto_max_record_count', 100)
            if auto_max_record_count == 500:
                auto_recording_settings['max_record_count_index'] = 1
            elif auto_max_record_count == 1000:
                auto_recording_settings['max_record_count_index'] = 2

            # 更新decoder_worker的自動錄製設定
            if hasattr(self, 'decoder_worker') and self.decoder_worker:
                self.decoder_worker.configure_auto_recording(auto_recording_settings)
                logger.info(f"Auto recording settings updated for connected tool {tool_data.get('toolname', 'Unknown')}")

        # 更新 UI
        self.view.show_Toolinfo_window(tool_data)


    def reset_angle(self):
        """處理 ResetAngle 按鈕事件"""
        if self.view.Btn4_ResetAngle.isChecked():
            logger.debug("Btn4_ResetAngle 狀態：On")
            self.view.opengl_widget_instance_1.view.setCameraPosition( elevation=90, azimuth=270)

    def plot_mode(self): 
        """處理 PlotMode 按鈕事件"""
        if not self.view.Btn3_PlotMode.isChecked():
            return
        
        logger.debug("Btn3_PlotMode 狀態：On")

        if self.view.plot_type == 'ADXL':
            # ADXL 模式下，只更動 previous_plot_type，不影響 plot_type
            self.previous_plot_type = 'line' if self.previous_plot_type == 'scatter' else 'scatter'
            logger.debug(f"ADXL 模式下更改 previous_plot_type: {self.previous_plot_type}")
        else:
            # scatter 或 line 模式下，直接切換 plot_type
            self.view.plot_type = 'line' if self.view.plot_type == 'scatter' else 'scatter'

        if hasattr(self, 'data'):
            self.update_holder_data(self.data)

    def BG_mode(self): 
        """處理 BGMode 按鈕事件"""
        if not self.view.Btn2_BGMode.isChecked():
            return

        logger.debug("Btn2_BGMode 狀態：On")

        sample_rate = self.current_data['sample_rate']
        self.view.opengl_widget_instance_1.SamplePoint2 = math.ceil(sample_rate / 12.5) // 200

        # 記錄或切換 plot_type
        if self.view.plot_type == 'ADXL':
            self.view.plot_type = getattr(self, 'previous_plot_type', 'scatter')  # 回復先前狀態，預設 'scatter'
        else:
            if self.view.plot_type in ('scatter', 'line'):
                self.previous_plot_type = self.view.plot_type  # 記錄當前狀態
            self.view.plot_type = 'ADXL'  # 切換到 ADXL

        self.set_camera_position(self.view.plot_type)

        if hasattr(self, 'data'):
            self.update_holder_data(self.data)

        #TODO: ADXL時colorbar顯示需要更換

    def set_camera_position(self, plot_type):
        """根據 plot_type 設置相機位置"""
        if plot_type == 'ADXL' :
            distance = 250000 
        else :
            distance = self.view.view_setting_data["CF_Auto_value"]["maximum"] * 150
        self.view.opengl_widget_instance_1.view.setCameraPosition(distance=distance)

        
    def show_target(self):
        """處理Target按鈕事件"""
        if self.view.Btn8_target.isChecked():
            logger.debug("Btn8_target 狀態：On")
            self.view.opengl_widget_instance_1.update_targets(True)
        else:
            logger.debug("Btn8_target 狀態：Off")
            self.view.opengl_widget_instance_1.update_targets(False)

    def handle_angle_button(self):
        """處理角度按鈕點擊事件"""
        if self.is_long_press:
            self.is_long_press = False
            return
            
        if self.saved_angle is None:
            self.saved_angle = self.view.opengl_widget_instance_1.view.cameraParams()
            logger.info(f"已儲存角度")
            self.view.Btn6_angle1.setIcon(QIcon(u":/btn_angle/btn_angle_1_0.png"))
            self.view.Btn6_angle1.setChecked(True)
        else:
            logger.info("切換到儲存的角度")
            valid_params = {key: self.saved_angle[key] for key in ['distance', 'elevation', 'azimuth'] if key in self.saved_angle}
            self.view.opengl_widget_instance_1.view.setCameraPosition(**valid_params)

    def start_cancel_angle(self):
        """開始角度按鈕長按計時"""
        self.cancel_timer = QTimer()
        self.cancel_timer.setSingleShot(True)
        self.cancel_timer.timeout.connect(self.cancel_saved_angle)
        self.cancel_timer.start(2000)

    def stop_cancel_angle(self):
        """停止角度按鈕長按計時"""
        if hasattr(self, 'cancel_timer') and self.cancel_timer.isActive():
            self.cancel_timer.stop()
            
    def cancel_saved_angle(self):
        """取消已儲存的角度"""
        if self.saved_angle is not None:
            self.saved_angle = None
            logger.info("已取消儲存的角度")
            self.view.Btn6_angle1.setIcon(QIcon(u":/btn_angle/btn_angle_1_2.png"))
            self.view.Btn6_angle1.setChecked(False)
            self.is_long_press = True 

    def bending_visibility(self, axis):
        """切換 BendingX_Visible, BendingY_Visible, BendingXY_Visible 的值並更新圖表"""
        if axis == "X":
            # 反轉當前狀態
            current_state = self.view.opengl_widget_bending.BendingX_Visible
            self.view.opengl_widget_bending.BendingX_Visible = not current_state
        elif axis == "Y":
            current_state = self.view.opengl_widget_bending.BendingY_Visible
            self.view.opengl_widget_bending.BendingY_Visible = not current_state
        elif axis == "XY":
            current_state = self.view.opengl_widget_bending.BendingXY_Visible
            self.view.opengl_widget_bending.BendingXY_Visible = not current_state
        
        # 可能需要更新圖表顯示
        self.view.opengl_widget_bending.redraw()  # 假設有這個方法來更新圖表

    def start_threads(self):
        """ 只啟動一次執行緒，避免重複創建"""
        if not self.threads_running:
            logger.info("啟動執行緒")
    
            if self.decoder_worker.tare_state:
                self.decoder_worker.tare_state = False
    
            # self.threads_running = True
    
            self.threads_running = True
            self.socket_thread.start()
            self.decoder_thread.start()
            self.decoder_worker.start()
    
            self.socket_worker.resume()  #  直接恢復 
            self.decoder_worker.resume()  #  直接恢復 `DecoderWorker`，而非重新啟動

    def stop_threads(self):       
        """ 暫停執行緒，而非完全停止"""
        if self.threads_running:
            logger.info("暫停執行緒")
            self.threads_running = False

            #  讓 Worker 停止處理數據，但不中斷執行緒
            self.decoder_worker.pause()
            self.socket_worker.pause()

            self.switch_decoder_slot(self.update_holder_data)

            if self.count > 1:
                # 更新 DB Tare 資料
                self.model.connect_db()
                self.model.update_tare_data(
                    self.current_data, 
                    self.tare_BendingX, 
                    self.tare_BendingY, 
                    self.tare_Tension, 
                    self.tare_Torsion
                )
                self.model.close_db()

                #  更新 Tare 資料
                self.decoder_worker.Tare_BX = self.tare_BendingX
                self.decoder_worker.Tare_BY = self.tare_BendingY
                self.decoder_worker.Tare_BZ = self.tare_Tension

            #  清空變數
            self.tare_BendingX = None
            self.tare_BendingY = None
            self.tare_Tension = None
            self.tare_Torsion = None
            self.count = 0

    def resume_threads(self):
        """ 恢復數據處理"""
        if not self.threads_running:
            logger.info("恢復執行緒")
            self.threads_running = True
            self.decoder_worker.resume()
            self.socket_worker.resume()
    
    def del_threads(self):
        """✅ 安全停止所有執行緒"""

        logger.info("停止執行緒")
        self.threads_running = False
        logger.debug("2del_threads")

        # ✅ 先停止 Worker，確保 `run()` 結束
        if hasattr(self, 'socket_worker'):
            # 禁用自動重連，避免在停止過程中重連
            self.socket_worker.set_auto_reconnect(False)
            self.socket_worker.stop()

        if hasattr(self, 'decoder_worker'):
            self.decoder_worker.stop()

        # ✅ 再結束執行緒，確保 `run()` 已結束
        self.socket_thread.quit()
        self.decoder_thread.quit()

        # ✅ 限制 `wait()` 最多 1 秒，避免 UI 卡住
        if not self.socket_thread.wait(1000):
            logger.warning("SocketWorker 未能正常結束，強制終止")
            self.socket_thread.terminate()
            self.socket_thread.wait()
        if not self.decoder_thread.wait(1000):
            logger.warning("DecoderWorker 未能正常結束，強制終止")
            self.decoder_thread.terminate()
            self.decoder_thread.wait()

    # 連線準備 WORKER 設置
    def get_current_data(self,data):
        """取得當前刀把設定資料"""
        try:
            if self.current_data is not None:
                # 檢查是否需要重新初始化
                need_reinit = False
                if self.current_data["toolip"] != data["toolip"]:
                    logger.debug("刀把 IP 改變，需要重新初始化")
                    need_reinit = True
                elif not hasattr(self, 'socket_worker') or not hasattr(self, 'decoder_worker'):
                    logger.debug("Worker 不存在，需要重新初始化")
                    need_reinit = True
                elif not self.socket_worker.running:
                    logger.debug("SocketWorker 未運行，需要重新初始化")
                    need_reinit = True

                if need_reinit:
                    logger.debug("正在重新初始化連接")
                    if hasattr(self, 'socket_worker'):
                        self.socket_worker.stop()
                    if hasattr(self, 'decoder_worker'):
                        self.decoder_worker.stop()
                    if self.socket_thread.isRunning():
                        self.socket_thread.quit()
                    if self.decoder_thread.isRunning():
                        self.decoder_thread.quit()
                else:
                    logger.debug("保持現有連接")
                    return
        except Exception as e:
            logger.error(f"Error in get_current_data: {e}")
            return

        if not isinstance(data, dict): # 確保 data 是字典
            logger.error(f"Unexpected data type for current_data: {type(data)}")
            return
        
        self.current_data = data
        logger.debug(f"current_data: {self.current_data}")
        self.view.set_Main_Window(data)
        toolholder_ip = data["toolip"]
        self.CO2_id = data["CO2_id"]

        if self.CO2_id is None: # 刀把若無相對應CO2_id
            logger.error("CO2_id is None")
            self.model.connect_db()
            CO2_data = self.view.CO2_data # 初始化CO2資料
            self.CO2_id = self.model.insert_CO2_data(CO2_data)
            self.model.update_tool_magazine_co2id(self.current_data, self.CO2_id)
            self.model.close_db()

        # 創建新的 worker
        self.socket_worker = SocketWorker(host=toolholder_ip)
        self.decoder_worker = DecoderWorker(data)
        self.decoder_worker.Dynamic_Tare_Count = [0, False]
        self.decoder_worker.filter_data = self.view.filter_data # 初始化濾波資料
        self.decoder_worker.CO2_data = self.view.CO2_data # 初始化CO2資料
        self.decoder_worker.update_msra_file_data(self.msra_file_path, self.msra_file_name)

        # 配置自動錄製設定
        # 產生刀把資訊文字(msra檔第一行)
        tool_info_text = f"!,{data['sample_rate']},{round(data['tare_xv'],self.record_decimal_places)},{round(data['tare_yv'],self.record_decimal_places)},{round(data['tare_zv'],self.record_decimal_places)},{round(data['tare_tv'],self.record_decimal_places)},{data['Linear_x']},{data['Linear_y']},{data['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{data['Lc']},{data['Hl']},{data['Kl']},{data['Bx_COMP']},{data['By_COMP']},{data['Bz_COMP']},{data['Bt_COMP']}\n"


        auto_recording_settings = {
            'auto_record_enabled': data.get('auto_record_enabled', False),
            'max_record_count_index': 0,  # 將根據 auto_max_record_count 計算
            'auto_record_seconds': data.get('auto_record_seconds', 10),
            'auto_pre_record_seconds': data.get('auto_pre_record_seconds', 0),
            'auto_cf_enabled': data.get('auto_cf_enabled', False),
            'auto_cf_threshold': data.get('auto_cf_threshold', 0),
            'auto_fz_enabled': data.get('auto_fz_enabled', False),
            'auto_fz_threshold': data.get('auto_fz_threshold', 0),
            'auto_t_enabled': data.get('auto_t_enabled', False),
            'auto_t_threshold': data.get('auto_t_threshold', 0),
            'tool_info_text': tool_info_text,
        }
        
        # 計算 max_record_count_index 基於 auto_max_record_count
        auto_max_record_count = data.get('auto_max_record_count', 100)
        if auto_max_record_count == 500:
            auto_recording_settings['max_record_count_index'] = 1
        elif auto_max_record_count == 1000:
            auto_recording_settings['max_record_count_index'] = 2
        self.decoder_worker.configure_auto_recording(auto_recording_settings)

        # 設置自動重連參數
        self.socket_worker.set_auto_reconnect(
            enabled=True,           # 啟用自動重連
            interval=5,             # 重連間隔5秒
            max_attempts=10         # 最多重連10次
        )
        self.socket_worker.set_connection_timeout(15)  # 連線超時15秒
        self.socket_worker.set_heartbeat_interval(30)  # 心跳檢測30秒

        # 連接信號
        self.socket_worker.raw_data_received.connect(self.decoder_worker.add_data)
        self.decoder_worker.decoded_data.connect(self.update_holder_data)
        self.socket_worker.sig_socket_connect.connect(self.socker_disconnection)
        self.socket_worker.sig_connection_status.connect(self.handle_connection_status)
        
        # 移動 Worker 到執行緒
        self.socket_worker.moveToThread(self.socket_thread)
        self.decoder_worker.moveToThread(self.decoder_thread)

        # 確保 Worker 在執行緒內運行
        self.socket_thread.started.connect(self.socket_worker.run)

    def socker_disconnection(self, data):
        # 判斷是不是斷線的刀把
        if data['host'] != self.current_data["toolip"]:
            return

        """處理 Socket 斷開連接"""
        self.view.Btn_Link.setChecked(data['status'])

        if data['status']:
            logger.info("Socket 連線成功")
            # 連線成功時的處理
        else:
            logger.warning("Socket 連線中斷")
            self.stop_threads()
            self.view.stop_timer()
            self.view.view_current_data.connect(self.get_current_data)

            # 檢查是否啟用自動重連
            if hasattr(self, 'socket_worker') and self.socket_worker.auto_reconnect:
                logger.info("自動重連已啟用，等待重連...")
                # 不顯示斷線提醒，讓自動重連處理
            else:
                self.view.show_Remind_window("刀把連接已斷開")

    def handle_connection_status(self, status):
        """處理連線狀態變化"""
        logger.info(f"連線狀態: {status}")

        # 可以在這裡更新 UI 狀態指示器
        # 例如：狀態欄顯示連線狀態
        if hasattr(self.view, 'update_connection_status'):
            self.view.update_connection_status(status)

    def configure_socket_reconnect(self, enabled=True, interval=5, max_attempts=10, timeout=15, heartbeat=30):
        """配置 Socket 自動重連參數

        Args:
            enabled (bool): 是否啟用自動重連
            interval (int): 重連間隔（秒）
            max_attempts (int): 最大重連次數，0表示無限重連
            timeout (int): 連線超時時間（秒）
            heartbeat (int): 心跳檢測間隔（秒）
        """
        if hasattr(self, 'socket_worker'):
            self.socket_worker.set_auto_reconnect(enabled, interval, max_attempts)
            self.socket_worker.set_connection_timeout(timeout)
            self.socket_worker.set_heartbeat_interval(heartbeat)
            logger.info(f"Socket 重連設定已更新: 啟用={enabled}, 間隔={interval}秒, 最大次數={max_attempts}")

    def force_socket_reconnect(self):
        """強制 Socket 重連"""
        if hasattr(self, 'socket_worker'):
            self.socket_worker.force_reconnect()
            logger.info("已觸發強制重連")

    def get_socket_status(self):
        """獲取 Socket 連線狀態"""
        if hasattr(self, 'socket_worker'):
            return self.socket_worker.get_connection_status()
        return None

    def disable_auto_reconnect(self):
        """禁用自動重連"""
        if hasattr(self, 'socket_worker'):
            self.socket_worker.set_auto_reconnect(False)
            logger.info("自動重連已禁用")

    def sleepmode_UP(self):
        if self.threads_running :
            if not self.decoder_worker.sleep_mode_temp:
                self.socket_worker.send_sleepmode(False) 
                self.boolean_sleep_mode = True
                self.view.Btn7_sleep_mode.setIcon(QIcon(u":/bnt_vp_sleep_mode/btn_vp_sleep_on.png"))  # 設定為開啟狀態的圖片
            else:
                self.socket_worker.send_sleepmode(True)
                self.boolean_sleep_mode = False
                self.view.Btn7_sleep_mode.setIcon(QIcon(u":/bnt_vp_sleep_mode/btn_vp_sleep_off.png"))  # 設定為關閉狀態的圖片
        
    def update_holder_data(self, data):
         # TAG: 濾波器參數更新
        self.decoder_worker.filter_data = self.view.filter_data
        self.decoder_worker.CO2_data = self.view.CO2_data

        self.view.update_holder_data(data)

    def update_tare_holder(self, data):
        """ 靜態 Tare 資料 預設 10 秒"""
        self.count += 1
        MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion,  MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, temp_RSSI ,Charging_Flag_temp ,sleep_mode_temp, temp_battery = data

        # if not self.decoder_worker.tare_state:
        #     self.decoder_worker.tare_state = True
        # self.decoder_worker.tare_state = True
        # logger.error(f"self.decoder_worker: {self.decoder_worker.tare_state}")
        # logger.error(MS_BendingX)

        if self.count == 1:
            self.tare_BendingX = np.mean(MS_BendingX)
            self.tare_BendingY = np.mean(MS_BendingY)
            self.tare_Tension = np.mean(MS_Tension)
            self.tare_Torsion = np.mean(MS_Torsion)
            #
            self.tare_ADXL_X = np.mean(MS_ADXL_X)
            self.tare_ADXL_Y = np.mean(MS_ADXL_Y)
            self.tare_ADXL_Z = np.mean(MS_ADXL_Z)

            # 儲存刀把系統檢查用的資料
            self.RSSI_max = temp_RSSI
            self.RSSI_min = temp_RSSI
            self.temperature_max = max(MS_Temperature)
            self.battery_min = temp_battery

            self.bx_max = max(MS_BendingX)
            self.bx_min = min(MS_BendingX)
            self.by_max = max(MS_BendingY)
            self.by_min = min(MS_BendingY)
            self.tension_max = max(MS_Tension)
            self.tension_min = min(MS_Tension)
            self.torsion_max = max(MS_Torsion)
            self.torsion_min = min(MS_Torsion)
           
            self.ax_max = max(MS_ADXL_X)
            self.ax_min = sys.maxsize
            self.ay_max = max(MS_ADXL_Y)
            self.ay_min = sys.maxsize
            self.az_max = max(MS_ADXL_Z)
            self.az_min = sys.maxsize

        else:
            # 以下算式的正確性?  
            # mean([new_data, old_mean]) -> ?
            # mean([new_mean, old_mean]) -> should becorrect

            # combined = MS_BendingX + [self.tare_BendingX]
            combined_BendingX = np.append(MS_BendingX, self.tare_BendingX)
            combined_BendingY = np.append(MS_BendingY, self.tare_BendingY)
            combined_Tension = np.append(MS_Tension, self.tare_Tension)
            combined_Torsion = np.append(MS_Torsion, self.tare_Torsion)
            #
            combined_ADXL_X = np.append(MS_ADXL_X, self.tare_ADXL_X)
            combined_ADXL_Y = np.append(MS_ADXL_Y, self.tare_ADXL_Y)
            combined_ADXL_Z = np.append(MS_ADXL_Z, self.tare_ADXL_Z)
           
            self.tare_BendingX = np.mean(combined_BendingX)
            self.tare_BendingY = np.mean(combined_BendingY)
            self.tare_Tension = np.mean(combined_Tension)
            self.tare_Torsion = np.mean(combined_Torsion)
            #
            self.tare_ADXL_X = np.mean(combined_ADXL_X)
            self.tare_ADXL_Y = np.mean(combined_ADXL_Y)
            self.tare_ADXL_Z = np.mean(combined_ADXL_Z)

            # 儲存刀把系統檢查用的資料
            # self.RSSI_max = max([self.RSSI_max, temp_RSSI])
            # self.RSSI_min = min([self.RSSI_min, temp_RSSI])
            self.temperature_max = max([self.temperature_max, max(MS_Temperature)])
            self.battery_min = min([self.battery_min, temp_battery])

            self.bx_max = max([self.bx_max, max(MS_BendingX)])
            self.bx_min = min([self.bx_min, min(MS_BendingX)])
            self.by_max = max([self.by_max, max(MS_BendingY)])
            self.by_min = min([self.by_min, min(MS_BendingY)])
            self.tension_max = max([self.tension_max, max(MS_Tension)])
            self.tension_min = min([self.tension_min, min(MS_Tension)])
            self.torsion_max = max([self.torsion_max, max(MS_Torsion)])
            self.torsion_min = min([self.torsion_min, min(MS_Torsion)])
           
            self.ax_max = max([self.ax_max, max(MS_ADXL_X)])
            self.ax_min = min([self.ax_min, min(MS_ADXL_X)])
            self.ay_max = max([self.ay_max, max(MS_ADXL_Y)])
            self.ay_min = min([self.ay_min, min(MS_ADXL_Y)])
            self.az_max = max([self.az_max, max(MS_ADXL_Z)])
            self.az_min = min([self.az_min, min(MS_ADXL_Z)])

        logger.info(f"tare_BendingX: {self.tare_BendingX}")
    
    def update_co2_data(self,CO2_data):
        """更新 CO2 資料"""
        # logger.debug(f"更新 CO2 資料: {CO2_data}")
        # 更新資料庫
        self.model.connect_db()
        self.model.update_co2_data(self.CO2_id,CO2_data)
        self.model.close_db()

    def update_msra_file_data(self,msra_file_data):
        """更新 msra_file_data"""
        self.msra_file_name = msra_file_data["msra_file_name"]
        self.msra_file_path = msra_file_data["msra_file_path"]
        logger.debug(f"msra_file_name: {self.msra_file_name}")
        logger.debug(f"msra_file_path: {self.msra_file_path}")

        # 新增：取得 save_rawdatafile (checkbox) 狀態並傳給 decoder_worker
        save_rawdatafile = msra_file_data.get("save_rawdatafile", False)
        if hasattr(self, 'decoder_worker'):
            self.decoder_worker.save_rawdatafile = save_rawdatafile
        else: 
            self.setting_data["save_rawdatafile"] = save_rawdatafile


        # 更新資料庫
        self.model.connect_db()
        self.model.update_file_path(self.msra_file_path)
        self.model.close_db()

        if hasattr(self, 'decoder_worker'):
            self.decoder_worker.update_msra_file_data(self.msra_file_path, self.msra_file_name)
            self.decoder_worker.set_auto_recording_file_settings(self.msra_file_path, "auto_record")

    # TAG: save_read_msrb_file
    def save_read_msrb_file(self, msrb_file_data):
        """儲存/讀取 msrb_file_data"""
       
        if msrb_file_data.get("action") == "save":

            directory = msrb_file_data.get("msrb_file_path")
            # logger.debug(f"Xdirectory: {directory}")
            file_manager = TextFileManager(directory)
            # logger.debug(f"執行儲存： {msrb_file_data}")
            file_manager.create_file(msrb_file_data.get("file_name"))

            # 格式化
            data = msrb_file_data.get("data")
            data_lines = [
                f"{data['CF_Auto_value']['maximum']},{data['CF_Auto_value']['minimum']},{data['CF_Auto_value']['warning']},{data['CF_Auto_value']['alarm']}",
                f"{data['FxFy_Auto_value']['peak_plus']},{data['FxFy_Auto_value']['peak_minus']}",
                f"{data['Fz_Auto_value']['peak_plus']},{data['Fz_Auto_value']['peak_minus']},{data['Fz_Auto_value']['alarm_plus']},{data['Fz_Auto_value']['alarm_minus']},{data['Fz_Auto_value']['warning_plus']},{data['Fz_Auto_value']['warning_minus']}",
                f"{data['Torque_Auto_value']['peak_plus']},{data['Torque_Auto_value']['peak_minus']},{data['Torque_Auto_value']['alarm_plus']},{data['Torque_Auto_value']['alarm_minus']},{data['Torque_Auto_value']['warning_plus']},{data['Torque_Auto_value']['warning_minus']}",
                f"{data['Temp_Auto_value']['maximum']},{data['Temp_Auto_value']['minimum']},{data['Temp_Auto_value']['warning']},{data['Temp_Auto_value']['alarm']}"
            ]

            formatted_text = "\n".join(data_lines) + "\n"  # 最後加 \n 保證檔案結尾換行
            file_manager.write_text(msrb_file_data.get("file_name"), formatted_text)


            # 重新開啟視窗
            self.show_View_window()

        elif msrb_file_data.get("action") == "read":

            current_dir = os.path.abspath(os.getcwd())
            # directory = current_dir + "/ViewSetting"
            directory = os.path.join(current_dir, "ViewSetting")
            file_manager = TextFileManager(directory)

            file_path = file_manager.choose_file()
            data = file_manager.load_file(file_path)
            logger.error(f"使用者選擇檔案：{file_path}")
            # 整理資料 陣列化
            lines = data.strip().splitlines()
            parsed_lines = [list(map(float, line.strip().split(','))) for line in lines]
            # 執行紀錄：[[10.0, 0.0, 0.0, 0.0], [20.0, -20.0], [30.0, 0.0, 0.0, -30.0, 0.0, 0.0], [40.0, 0.0, 0.0, -40.0, 0.0, 0.0], [60.0, 30.0, 50.0, 55.0]]
           
            logger.debug(f"讀取紀錄：{parsed_lines}")
            logger.debug(f"1_view_setting_data：{self.view.view_setting_data}")

            # 判斷資料讀取更新用
            self.view.view_read_data = True

            self.view.view_setting_data["CF_Auto_value"] = {
                "state": False,
                "maximum": parsed_lines[0][0],
                "minimum": parsed_lines[0][1],
                "warning": parsed_lines[0][2],
                "alarm": parsed_lines[0][3]
            }

            self.view.view_setting_data["FxFy_Auto_value"] = {
                "state": False,
                "peak_plus": parsed_lines[1][0],
                "peak_minus": parsed_lines[1][1],
            }

            self.view.view_setting_data["Fz_Auto_value"] = {
                "state": False,
                "peak_plus": parsed_lines[2][0],
                "peak_minus": parsed_lines[2][1],
                "alarm_plus": parsed_lines[2][2],
                "alarm_minus": parsed_lines[2][3],
                "warning_plus": parsed_lines[2][4],
                "warning_minus": parsed_lines[2][5],
            }

            self.view.view_setting_data["Torque_Auto_value"] = {
                "state": False,
                "peak_plus": parsed_lines[3][0],
                "peak_minus": parsed_lines[3][1],
                "alarm_plus": parsed_lines[3][2],
                "alarm_minus": parsed_lines[3][3],
                "warning_plus": parsed_lines[3][4],
                "warning_minus": parsed_lines[3][5],
            }

            self.view.view_setting_data["Temp_Auto_value"] = {
                "state": False,
                "maximum": parsed_lines[4][0],
                "minimum": parsed_lines[4][1],
                "warning": parsed_lines[4][2],
                "alarm": parsed_lines[4][3],
            }
            
            logger.debug(f"2_view_setting_data：{self.view.view_setting_data}")

            # 重新開啟視窗
            self.show_View_window()
            
        else:
            logger.debug(f"未知動作：{msrb_file_data}")

        return

        
    # def widget_bendingX_visible(self):
    #     """處理按鈕事件"""
    #     if self.view.Fx_btn.isChecked():
    #         logger.debug("Btn_Link 狀態：On")
    #         # self.view_opengl_data.connect(self.view.update_holder_data)
    #         self.start_threads()
    #         self.view.opengl_widget_bending.BendingX_Visible = True
    #     else:
    #         logger.debug("Btn_Link 狀態：Off")
    #         self.view.opengl_widget_bending.BendingX_Visible = False

    
    # 信號槽 用來切換解碼器的信號槽函數
    def switch_decoder_slot(self, new_slot):
        try:
            self.decoder_worker.decoded_data.disconnect()
        except TypeError:
            pass  # 若尚未連接，會拋出 TypeError，這裡忽略即可
        
        self.decoder_worker.decoded_data.connect(new_slot)
    
    def search_network_devices(self):
        """搜尋網路設備並更新狀態"""
        try:
            # 使用類變數來存儲上次搜尋的結果和時間
            if not hasattr(self, '_last_search_time'):
                self._last_search_time = 0
                self._last_search_results = {}
            
            current_time = time.time()
            # 如果距離上次搜尋不到 5 秒，直接使用上次的結果
            if current_time - self._last_search_time < 5 and self._last_search_results:
                found_devices = self._last_search_results
                logger.info("使用快取的搜尋結果")
            else:
                # 執行新的搜尋
                devices = search_all_network_devices()
                
                if devices:
                    found_devices = devices
                    # 更新快取
                    self.view.updateConnect(found_devices)
                    self._last_search_results = found_devices
                    self._last_search_time = current_time
                    logger.info(f"找到的設備數量: {len(devices)}")
                else:
                    found_devices = {}
                    self._last_search_results = {}
                    self._last_search_time = current_time
                    logger.info("未找到任何設備")
            
            return found_devices
                
        except Exception as e:
            logger.error(f"搜尋網路設備時發生錯誤: {e}")
            return {}
    